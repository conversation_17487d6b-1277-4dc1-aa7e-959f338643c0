parameters:
- name: CONTRIES
  displayName: "Contries"
  type: string
  default: "exemple:itf,iti,ite,itp,its"
- name: NEW_REPO_NAME
  displayName: "New microservice repo name"
  type: string
  default: "exemple:IT.Microservices.Stuff.Doodler"
- name: TYPE
  displayName: "New microservice's type"
  type: string
  default: "application"
  values:
    - "application"
    - "cronjob"

pool:
  vmImage: ubuntu-latest

stages:
  - stage: Create_cicd_for_new_ms
    displayName: Create cicd for new ms
    jobs:
      - job: Build       
        displayName: 'Create cicd for new ms'
        steps:
        - checkout: self
          persistCredentials: true
        - task: Bash@3
          name: Create_cicd
          displayName: 'create cicd'
          inputs:
            workingDirectory: $(System.DefaultWorkingDirectory)
            filePath: scripts/create_cicd_for_repo.sh
            arguments: "--repo \"${{ parameters.NEW_REPO_NAME }}\" --contry \"${{ parameters.CONTRIES }}\" --type \"${{ parameters.TYPE }}\""
          env:
            AZDEVOPS_USER: $(AZDEVOPS_USER)
            AZDEVOPS_PASSWORD: $(AZDEVOPS_PASSWORD)
            ACR_CRED_SHARED: $(ACR_CRED_SHARED)
            ACR_CRED_DEV: $(ACR_CRED_DEV)
            ACR_CRED_RECETTE: $(ACR_CRED_RECETTE)
            ACR_CRED_PERF: $(ACR_CRED_PERF)
            ACR_CRED_PREPROD: $(ACR_CRED_PREPROD)
            ACR_CRED_PROD: $(ACR_CRED_PROD)
            SP_SECRET_DEV: $(SP_SECRET_DEV)
            SP_SECRET_RECETTE: $(SP_SECRET_RECETTE)
            SP_SECRET_PERF: $(SP_SECRET_PERF)
            SP_SECRET_PREPROD: $(SP_SECRET_PREPROD)
            SP_SECRET_PROD: $(SP_SECRET_PROD)