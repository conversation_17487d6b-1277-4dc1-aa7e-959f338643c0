# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# V.C 15/05/2024

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/ITF.Microservices.Template.Cronjob.csproj", "/src/ITF.Microservices.Template.Cronjob/"]
COPY ["src/NuGet.config", "/src/ITF.Microservices.Template.Cronjob/"]

RUN dotnet restore "ITF.Microservices.Template.Cronjob/ITF.Microservices.Template.Cronjob.csproj"

WORKDIR "/src/ITF.Microservices.Template.Cronjob"

COPY . .

RUN dotnet build "src/ITF.Microservices.Template.Cronjob.csproj" -c Release
RUN dotnet test "tests/ITF.Microservices.Template.Cronjob.UnitTests/ITF.Microservices.Template.Cronjob.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/ITF.Microservices.Template.Cronjob.csproj" -c Release -o out

ENTRYPOINT sleep 10000