{"version": 3, "targets": {}, "libraries": {}, "projectFileDependencyGroups": {}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"restore": {"projectUniqueName": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj", "projectPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\obj\\", "projectStyle": "PackageReference", "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://interflorad365fo.pkgs.visualstudio.com/_packaging/ITF.SharedLibraries/nuget/v3/index.json": {}}}}, "logs": [{"code": "NU1105", "level": "Error", "message": "Impossible de lire les informations relatives au projet pour 'ITF.Microservices.Template.Reactor' : 'SHAREDLIB_FRAMEWORK_VERSION' n'est pas une chaîne de version valide."}]}