# ITF.Microservices.Template.Reactor

Write a description here ...

## Docker compose

Describe the docker compose

```dockerfile
  IMAGE_CONTAINER_NAME:
    image: ${DOCKER_REGISTRY-}IMAGE_CONTAINER_NAME
    container_name: IMAGE_CONTAINER_NAME
    build:
      context: .
      dockerfile: src/ITF.Microservices.Template.Reactor/src/Dockerfile
```

```dockerfile
  IMAGE_CONTAINER_NAME:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - UNLEASH__KEY=UNLEASH_KEY
    ports:
      - "99999:80"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
```

## Settings

Indicate here the list of specific settings here, and their purpose

```json
  "SettingExample": {
    "Setting1": "http://someurl:9999",
    "Enabled": true,
    "SubParameter1": "none"
  }
```

## Usage / examples

Indicate here some examples of how to use it ...

```bash
# Perform a call and return something ...
curl --location --request GET 'http://localhost:99999/api/Any/Something?id=1234' \
--header 'Content-Type: application/json' 
```
