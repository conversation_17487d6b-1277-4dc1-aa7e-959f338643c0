#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
ENV ASPNETCORE_HTTP_PORTS=80
ENV ASPNETCORE_URLS=http://*:80
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/ITF.Microservices.Template.Api/ITF.Microservices.Template.Api.csproj", "src/ITF.Microservices.Template.Api/"]
RUN dotnet restore "src/ITF.Microservices.Template.Api/ITF.Microservices.Template.Api.csproj"
COPY . .
WORKDIR "/src/src/ITF.Microservices.Template.Api"
RUN dotnet build "ITF.Microservices.Template.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ITF.Microservices.Template.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ITF.Microservices.Template.Api.dll"]