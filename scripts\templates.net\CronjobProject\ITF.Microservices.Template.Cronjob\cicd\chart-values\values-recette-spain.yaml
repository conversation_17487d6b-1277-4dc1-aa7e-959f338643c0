###
# Values for recette environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/ITF.Microservices.Template.Cronjob/"
#helm diff upgrade IMAGE_CONTAINER_NAME ${helmChartPath} --values chart-values/values-recette-spain.yaml -n ite-ms --set 'image.tag=latest,image.repository=iterecetteacr.azurecr.io/IMAGE_CONTAINER_NAME'

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

restartPolicy: Never

schedule: "SCHEDULE"

nameOverride: "IMAGE_CONTAINER_NAME"
fullnameOverride: "IMAGE_CONTAINER_NAME"



dotnetProgramName: "ITF.Microservices.Template.Cronjob.dll"
appStartCommand: "dotnet ITF.Microservices.Template.Cronjob.dll"

podAnnotations: []
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/IMAGE_CONTAINER_NAME.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "ite-microservices"
  # vault.hashicorp.com/agent-inject-secret-IMAGE_CONTAINER_NAME.pass: "applications/ite-microservices"

  # Inject secret via a configmap named IMAGE_CONTAINER_NAME-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'IMAGE_CONTAINER_NAME-secrets'
  
  #inject secret via env variables 
  #vault.hashicorp.com/agent-inject: 'true'
  #vault.hashicorp.com/role: 'ite-microservices'
  #vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/ite-microservices'
  #vault.hashicorp.com/agent-inject-template-config: |

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 
  serilogConfig: |-       
    "Serilog": {
      "Using": [],
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "Microsoft": "Warning",
          "System": "Information",
          "Elastic": "Warning",
          "Apm": "Warning"
        }
      },
      "WriteTo": [
        {
          "Name": "Console"
        }
      ],
      "Enrich": [
        "FromLogContext",
        "WithMachineName",
        "WithProcessId",
        "WithThreadId"
      ],
      "Properties": {
        "ApplicationName": "ite-IMAGE_CONTAINER_NAME"
      }
    }

env:
  - name: MongoDb__ConnectionString
    value: "mongodb://ite-ms-mongodb-0.ite-ms-mongodb-headless.ite-ms-common.svc.cluster.local:27017,ite-ms-mongodb-1.ite-ms-mongodb-headless.ite-ms-common.svc.cluster.local:27017,ite-ms-mongodb-2.ite-ms-mongodb-headless.ite-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: ElasticApm__Environment
    value: "ite-recette"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "ite-IMAGE_CONTAINER_NAME"    

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "ite-ms-kafka-headless.ite-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "ite-ms-redis-headless.ite-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://ite-unleash.ite-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "ite-IMAGE_CONTAINER_NAME"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "development"