#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
ITE.AvailabilityRules
ITF.AssignementRules
ITF.Availability.Library
ITF.InfrastructureAndSettings
ITF.Lib.Common
ITE.Order.Library
ITF.Order.Library
ITF.SharedAssignmentRules
ITF.SharedLibraries
ITF.SharedModels
ITI2.AvailabilityRules
ITP.AvailabilityRules
ITF.AvailabilityRules
ITS.AvailabilityRules
ITI2.AvailabilityRules
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY pull
done

REPOSITORIESOCTO=(
IT.Microservices.EmailSender
IT.Microservices.AbandonedCartDetector
IT.Microservices.AbandonedCartEmailSender
IT.Microservices.Availability
IT.Microservices.CMSDraft.Synchronizer
IT.Microservices.CT.Customer.Synchronizer
IT.Microservices.CT.Order.Synchronizer
IT.Microservices.CT.Product.Synchronizer
IT.Microservices.CT.Order.Wrapper
IT.Microservices.EmailEmarsysSender
IT.Microservices.Florist
IT.Microservices.Florist.Queries
IT.Microservices.GFS.Supplier
IT.Microservices.LengowFeed.Api
IT.Microservices.OrderConfirmationEmailSender
IT.Microservices.OrderLogHistoryReactor
IT.Microservices.OrderReactor
IT.Microservices.SequenceGenerator
ITE.Microservices.Availability.CatalogSubset.Synchronizer
ITE.Microservices.OrderSubset.Synchronizer
ITE.Microservices.ProductSubset.Synchronizer
ITF.Microservices.Availability
ITF.Microservices.Availability.CatalogSubset.Synchronizer
ITF.Microservices.Availability.FloristSubset.Synchronizer
ITF.Microservices.Availability.OrderSubset.Synchronizer
ITF.Microservices.Availability.Reactor
ITF.Microservices.Availability.StaticSubset.Provider
ITF.Microservices.CatalogSynchronizer
ITF.Microservices.CatalogSynchronizer.Reactor
ITF.Microservices.CourrierPoller
ITF.Microservices.CourrierSynchronizer
ITF.Microservices.CourrierWrapper
ITF.Microservices.FlashInfo
ITF.Microservices.FloristSettings
ITF.Microservices.FloristSynchronizer
ITF.Microservices.FreshPortal.Synchronizer
ITF.Microservices.LegacyFloristEvents
ITF.Microservices.LegacyOrderEvents
ITP.Microservices.Availability.CatalogSubset.Synchronizer
ITP.Microservices.OrderSubset.Synchronizer
ITP.Microservices.ProductSubset.Synchronizer
ITF.Microservices.OrderSubset.Synchronizer
ITF.Microservices.ProductSubset.Synchronizer
IT.Microservices.CRMFeeder
IT.Microservices.Bloomreach.Reactor
IT.Microservices.Bloomreach.Sync
IT.Microservices.HttpToKafkaWrapper
ITF.Microservices.FloristSubscription
IT.Microservices.NotificationSupplier
ITF.Microservices.Payment
ITF.Microservices.Selfcare
IT.Microservices.EmailEmarsysSender
IT.Microservices.EmailEmarsys.Reactor
ITF.Microservices.DiscountCodeSync
IT.Microservices.CT.BusinessUnit.Synchronizer
IT.Microservices.ApiExtensionCT.Order
ITF.Microservices.BillManagement
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================  
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done

  
