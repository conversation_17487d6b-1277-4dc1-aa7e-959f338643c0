# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# V.C 15/05/2024

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/ITF.Microservices.Template.Api.csproj", "/src/ITF.Microservices.Template.Api/"]
COPY ["src/NuGet.config", "/src/ITF.Microservices.Template.Api/"]

RUN dotnet restore "ITF.Microservices.Template.Api/ITF.Microservices.Template.Api.csproj"

WORKDIR "/src/ITF.Microservices.Template.Api"

COPY . .

RUN dotnet build "src/ITF.Microservices.Template.Api.csproj" -c Release
RUN dotnet test "tests/ITF.Microservices.Template.Api.UnitTests/ITF.Microservices.Template.Api.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/ITF.Microservices.Template.Api.csproj" -c Release -o out

ENTRYPOINT sleep 10000