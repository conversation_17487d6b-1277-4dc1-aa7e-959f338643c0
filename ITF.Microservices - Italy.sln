﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{6955FF57-95B0-41E6-8087-B797ADD1009D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{AAF33B63-5DA4-4565-9162-A82D22947351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries.UnitTests", "src\ITF.SharedLibraries\tests\ITF.SharedLibraries.UnitTests\ITF.SharedLibraries.UnitTests.csproj", "{********-7CD4-491B-8CEB-806CC9A51D35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels.UnitTests", "src\ITF.SharedModels\tests\ITF.SharedModels.UnitTests\ITF.SharedModels.UnitTests.csproj", "{5DCD0871-E480-4B7E-A432-248A47D5C165}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer", "src\IT.Microservices.CT.Product.Synchronizer\src\IT.Microservices.CT.Product.Synchronizer.csproj", "{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability", "src\IT.Microservices.Availability\src\IT.Microservices.Availability.csproj", "{BDBA0ED9-2678-4F4A-A6EB-7F019AE9D875}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer", "src\IT.Microservices.CT.Order.Synchronizer\src\IT.Microservices.CT.Order.Synchronizer.csproj", "{41423798-D421-44D1-AA10-15C28E5CBDAF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.LengowFeed.Api", "src\IT.Microservices.LengowFeed.Api\src\IT.Microservices.LengowFeed.Api.csproj", "{E82136D2-27AA-4DA1-B2B1-F5C90AEB3A58}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.LengowFeed.Api.UnitTests", "src\IT.Microservices.LengowFeed.Api\tests\IT.Microservices.LengowFeed.Api.UnitTests\IT.Microservices.LengowFeed.Api.UnitTests.csproj", "{47276197-3541-42B2-A3FA-06982793D00D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules", "src\ITP.AvailabilityRules\src\ITP.AvailabilityRules.csproj", "{4C3DA815-44BF-43A6-ABBA-A04E5A3549E0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules.UnitTests", "src\ITP.AvailabilityRules\tests\ITP.AvailabilityRules.UnitTests\ITP.AvailabilityRules.UnitTests.csproj", "{6D89224D-2511-43A3-AEB1-0287D4021CF3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules", "src\ITE.AvailabilityRules\src\ITE.AvailabilityRules.csproj", "{FED23328-BF52-449E-83FA-D078292EA109}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules.UnitTests", "src\ITE.AvailabilityRules\tests\ITE.AvailabilityRules.UnitTests\ITE.AvailabilityRules.UnitTests.csproj", "{0E912CB0-288B-424B-9E2D-D86CC09E383C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules.UnitTests", "src\ITF.AvailabilityRules\tests\ITF.AvailabilityRules.UnitTests\ITF.AvailabilityRules.UnitTests.csproj", "{AF44F8B9-AD62-4BA6-B4A8-863A9B47F720}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules", "src\ITF.AvailabilityRules\src\ITF.AvailabilityRules.csproj", "{EA1C5F2D-8C15-4316-AE76-D86BBE3AFDE9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.FloristReactor", "src\IT.Microservices.FloristReactor\src\IT.Microservices.FloristReactor.csproj", "{0D7E2097-F34A-493D-BD8B-53A729F6C617}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.FloristReactor.UnitTests", "src\IT.Microservices.FloristReactor\tests\IT.Microservices.FloristReactor.UnitTests\IT.Microservices.FloristReactor.UnitTests.csproj", "{9BEB0B59-74C0-413F-977E-07C89022C4BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.HttpToKafkaWrapper", "src\IT.Microservices.HttpToKafkaWrapper\src\IT.Microservices.HttpToKafkaWrapper.csproj", "{7AE14B60-EBA2-411E-8936-B0FA93F3DAB3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.HttpToKafkaWrapper.UnitTests", "src\IT.Microservices.HttpToKafkaWrapper\tests\IT.Microservices.HttpToKafkaWrapper.UnitTests\IT.Microservices.HttpToKafkaWrapper.UnitTests.csproj", "{113DFFF0-1070-48A6-A1DF-7E2513FEC239}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{D3C264F5-2DD3-4D32-856D-BD0FFCB7A4A5}"
	ProjectSection(SolutionItems) = preProject
		src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj = src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderReactor.UnitTests", "src\IT.Microservices.OrderReactor\tests\IT.Microservices.OrderReactor.UnitTests\IT.Microservices.OrderReactor.UnitTests.csproj", "{FE2FC618-9033-4941-BCCC-C86DFC3420B2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderReactor", "src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj", "{080D80C1-979C-4C13-BFC7-A62A118A87AB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.SequenceGenerator", "src\IT.Microservices.SequenceGenerator\src\IT.Microservices.SequenceGenerator.csproj", "{9F8F4F2B-BD29-4915-86BC-F325BD53FE85}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.SequenceGenerator.UnitTests", "src\IT.Microservices.SequenceGenerator\tests\IT.Microservices.SequenceGenerator.UnitTests\IT.Microservices.SequenceGenerator.UnitTests.csproj", "{1E119638-B2C7-43EF-AC08-1388702546E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT", "src\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj", "{2C71459C-3005-4BBF-B423-16FD39182E51}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.ProductSubset.Synchronizer", "src\ITE.Microservices.ProductSubset.Synchronizer\src\ITE.Microservices.ProductSubset.Synchronizer.csproj", "{754493B5-A51D-4CD4-8AB9-9F03C6F182D5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.OrderSubset.Synchronizer", "src\ITE.Microservices.OrderSubset.Synchronizer\src\ITE.Microservices.OrderSubset.Synchronizer.csproj", "{D06CB678-384C-42C4-8258-0DCA92FA120F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer\src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{8DC1827D-6BEB-4311-928E-A80C6CF79F0B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{4F44E6B3-5CFC-4BA0-B496-09CB1CB89004}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.OrderSubset.Synchronizer", "src\ITI2.Microservices.OrderSubset.Synchronizer\src\ITI2.Microservices.OrderSubset.Synchronizer.csproj", "{57236613-4086-4082-BAEE-EA5E6A8D3027}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.OrderSubset.Synchronizer\tests\ITI2.Microservices.OrderSubset.Synchronizer.UnitTests\ITI2.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{16366DBA-90F8-4085-8116-17720826898F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.ProductSubset.Synchronizer", "src\ITI2.Microservices.ProductSubset.Synchronizer\src\ITI2.Microservices.ProductSubset.Synchronizer.csproj", "{16641881-4C97-46C4-912B-9E6F814D35D1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.ProductSubset.Synchronizer\tests\ITI2.Microservices.ProductSubset.Synchronizer.UnitTests\ITI2.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{F9D73E69-45D6-4AFD-A732-CEF0D4A79567}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules", "src\ITI2.AvailabilityRules\src\ITI2.AvailabilityRules.csproj", "{736E45D7-6FB0-4572-B949-187F41F6243F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules.UnitTests", "src\ITI2.AvailabilityRules\tests\ITI2.AvailabilityRules.UnitTests\ITI2.AvailabilityRules.UnitTests.csproj", "{80BE3315-A0C4-417F-80C8-A892F9CEBC91}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Order.Library", "src\ITE.Order.Library\src\ITE.Order.Library.csproj", "{FFCEA4AD-0068-4114-9F59-FACF7D4E2B82}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Order.Library", "src\ITF.Order.Library\src\ITF.Order.Library.csproj", "{E9668E7A-379A-4114-96E7-46ED8D2CDC8C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.AvailabilityRules", "src\ITS.AvailabilityRules\src\ITS.AvailabilityRules.csproj", "{0542C87C-BCC6-4866-AE9A-12C56BA24FAE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BDBA0ED9-2678-4F4A-A6EB-7F019AE9D875}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BDBA0ED9-2678-4F4A-A6EB-7F019AE9D875}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BDBA0ED9-2678-4F4A-A6EB-7F019AE9D875}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BDBA0ED9-2678-4F4A-A6EB-7F019AE9D875}.Release|Any CPU.Build.0 = Release|Any CPU
		{41423798-D421-44D1-AA10-15C28E5CBDAF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41423798-D421-44D1-AA10-15C28E5CBDAF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41423798-D421-44D1-AA10-15C28E5CBDAF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41423798-D421-44D1-AA10-15C28E5CBDAF}.Release|Any CPU.Build.0 = Release|Any CPU
		{E82136D2-27AA-4DA1-B2B1-F5C90AEB3A58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E82136D2-27AA-4DA1-B2B1-F5C90AEB3A58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E82136D2-27AA-4DA1-B2B1-F5C90AEB3A58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E82136D2-27AA-4DA1-B2B1-F5C90AEB3A58}.Release|Any CPU.Build.0 = Release|Any CPU
		{47276197-3541-42B2-A3FA-06982793D00D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47276197-3541-42B2-A3FA-06982793D00D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47276197-3541-42B2-A3FA-06982793D00D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47276197-3541-42B2-A3FA-06982793D00D}.Release|Any CPU.Build.0 = Release|Any CPU
		{4C3DA815-44BF-43A6-ABBA-A04E5A3549E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4C3DA815-44BF-43A6-ABBA-A04E5A3549E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4C3DA815-44BF-43A6-ABBA-A04E5A3549E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4C3DA815-44BF-43A6-ABBA-A04E5A3549E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D89224D-2511-43A3-AEB1-0287D4021CF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D89224D-2511-43A3-AEB1-0287D4021CF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D89224D-2511-43A3-AEB1-0287D4021CF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D89224D-2511-43A3-AEB1-0287D4021CF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{FED23328-BF52-449E-83FA-D078292EA109}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FED23328-BF52-449E-83FA-D078292EA109}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FED23328-BF52-449E-83FA-D078292EA109}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FED23328-BF52-449E-83FA-D078292EA109}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E912CB0-288B-424B-9E2D-D86CC09E383C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E912CB0-288B-424B-9E2D-D86CC09E383C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E912CB0-288B-424B-9E2D-D86CC09E383C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E912CB0-288B-424B-9E2D-D86CC09E383C}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF44F8B9-AD62-4BA6-B4A8-863A9B47F720}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF44F8B9-AD62-4BA6-B4A8-863A9B47F720}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF44F8B9-AD62-4BA6-B4A8-863A9B47F720}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF44F8B9-AD62-4BA6-B4A8-863A9B47F720}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA1C5F2D-8C15-4316-AE76-D86BBE3AFDE9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA1C5F2D-8C15-4316-AE76-D86BBE3AFDE9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA1C5F2D-8C15-4316-AE76-D86BBE3AFDE9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA1C5F2D-8C15-4316-AE76-D86BBE3AFDE9}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D7E2097-F34A-493D-BD8B-53A729F6C617}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D7E2097-F34A-493D-BD8B-53A729F6C617}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D7E2097-F34A-493D-BD8B-53A729F6C617}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D7E2097-F34A-493D-BD8B-53A729F6C617}.Release|Any CPU.Build.0 = Release|Any CPU
		{9BEB0B59-74C0-413F-977E-07C89022C4BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9BEB0B59-74C0-413F-977E-07C89022C4BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9BEB0B59-74C0-413F-977E-07C89022C4BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9BEB0B59-74C0-413F-977E-07C89022C4BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7AE14B60-EBA2-411E-8936-B0FA93F3DAB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AE14B60-EBA2-411E-8936-B0FA93F3DAB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AE14B60-EBA2-411E-8936-B0FA93F3DAB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AE14B60-EBA2-411E-8936-B0FA93F3DAB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{113DFFF0-1070-48A6-A1DF-7E2513FEC239}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{113DFFF0-1070-48A6-A1DF-7E2513FEC239}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{113DFFF0-1070-48A6-A1DF-7E2513FEC239}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{113DFFF0-1070-48A6-A1DF-7E2513FEC239}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE2FC618-9033-4941-BCCC-C86DFC3420B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE2FC618-9033-4941-BCCC-C86DFC3420B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE2FC618-9033-4941-BCCC-C86DFC3420B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE2FC618-9033-4941-BCCC-C86DFC3420B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{080D80C1-979C-4C13-BFC7-A62A118A87AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{080D80C1-979C-4C13-BFC7-A62A118A87AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{080D80C1-979C-4C13-BFC7-A62A118A87AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{080D80C1-979C-4C13-BFC7-A62A118A87AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F8F4F2B-BD29-4915-86BC-F325BD53FE85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F8F4F2B-BD29-4915-86BC-F325BD53FE85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F8F4F2B-BD29-4915-86BC-F325BD53FE85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F8F4F2B-BD29-4915-86BC-F325BD53FE85}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E119638-B2C7-43EF-AC08-1388702546E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E119638-B2C7-43EF-AC08-1388702546E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E119638-B2C7-43EF-AC08-1388702546E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E119638-B2C7-43EF-AC08-1388702546E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Release|Any CPU.Build.0 = Release|Any CPU
		{754493B5-A51D-4CD4-8AB9-9F03C6F182D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{754493B5-A51D-4CD4-8AB9-9F03C6F182D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{754493B5-A51D-4CD4-8AB9-9F03C6F182D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{754493B5-A51D-4CD4-8AB9-9F03C6F182D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D06CB678-384C-42C4-8258-0DCA92FA120F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D06CB678-384C-42C4-8258-0DCA92FA120F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D06CB678-384C-42C4-8258-0DCA92FA120F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D06CB678-384C-42C4-8258-0DCA92FA120F}.Release|Any CPU.Build.0 = Release|Any CPU
		{8DC1827D-6BEB-4311-928E-A80C6CF79F0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8DC1827D-6BEB-4311-928E-A80C6CF79F0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8DC1827D-6BEB-4311-928E-A80C6CF79F0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8DC1827D-6BEB-4311-928E-A80C6CF79F0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F44E6B3-5CFC-4BA0-B496-09CB1CB89004}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F44E6B3-5CFC-4BA0-B496-09CB1CB89004}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F44E6B3-5CFC-4BA0-B496-09CB1CB89004}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F44E6B3-5CFC-4BA0-B496-09CB1CB89004}.Release|Any CPU.Build.0 = Release|Any CPU
		{57236613-4086-4082-BAEE-EA5E6A8D3027}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57236613-4086-4082-BAEE-EA5E6A8D3027}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57236613-4086-4082-BAEE-EA5E6A8D3027}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57236613-4086-4082-BAEE-EA5E6A8D3027}.Release|Any CPU.Build.0 = Release|Any CPU
		{16366DBA-90F8-4085-8116-17720826898F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16366DBA-90F8-4085-8116-17720826898F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16366DBA-90F8-4085-8116-17720826898F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16366DBA-90F8-4085-8116-17720826898F}.Release|Any CPU.Build.0 = Release|Any CPU
		{16641881-4C97-46C4-912B-9E6F814D35D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16641881-4C97-46C4-912B-9E6F814D35D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16641881-4C97-46C4-912B-9E6F814D35D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16641881-4C97-46C4-912B-9E6F814D35D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9D73E69-45D6-4AFD-A732-CEF0D4A79567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9D73E69-45D6-4AFD-A732-CEF0D4A79567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9D73E69-45D6-4AFD-A732-CEF0D4A79567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9D73E69-45D6-4AFD-A732-CEF0D4A79567}.Release|Any CPU.Build.0 = Release|Any CPU
		{736E45D7-6FB0-4572-B949-187F41F6243F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{736E45D7-6FB0-4572-B949-187F41F6243F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{736E45D7-6FB0-4572-B949-187F41F6243F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{736E45D7-6FB0-4572-B949-187F41F6243F}.Release|Any CPU.Build.0 = Release|Any CPU
		{80BE3315-A0C4-417F-80C8-A892F9CEBC91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{80BE3315-A0C4-417F-80C8-A892F9CEBC91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{80BE3315-A0C4-417F-80C8-A892F9CEBC91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{80BE3315-A0C4-417F-80C8-A892F9CEBC91}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFCEA4AD-0068-4114-9F59-FACF7D4E2B82}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFCEA4AD-0068-4114-9F59-FACF7D4E2B82}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFCEA4AD-0068-4114-9F59-FACF7D4E2B82}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFCEA4AD-0068-4114-9F59-FACF7D4E2B82}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9668E7A-379A-4114-96E7-46ED8D2CDC8C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9668E7A-379A-4114-96E7-46ED8D2CDC8C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9668E7A-379A-4114-96E7-46ED8D2CDC8C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9668E7A-379A-4114-96E7-46ED8D2CDC8C}.Release|Any CPU.Build.0 = Release|Any CPU
		{0542C87C-BCC6-4866-AE9A-12C56BA24FAE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0542C87C-BCC6-4866-AE9A-12C56BA24FAE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0542C87C-BCC6-4866-AE9A-12C56BA24FAE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0542C87C-BCC6-4866-AE9A-12C56BA24FAE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FFDA7BF3-82E4-47E6-9C00-D529305595ED}
	EndGlobalSection
EndGlobal
