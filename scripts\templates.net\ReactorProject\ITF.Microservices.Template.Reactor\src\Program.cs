global using ITF.Microservices.Template.Reactor.MyHandler;


var builder = WebApplication.CreateBuilder(args);

builder.Host.ConfigureHostBuilder(); // Global Inteflora Config on the Host (Logging / Config)

//** Registering Services Part **
builder.Services.UseCommercetoolsApiSerialization();

builder.Services
    .AddHealthChecksMiddleware()
    .AddAllMetrics()
    .UseFeatureFlags(builder.Configuration)
    .AddSwagger(Assembly.GetExecutingAssembly().GetName().Name!)
    .AddScoped<IMyUseCase, MyUseCase>()
    .AddScoped<IMyFacade, MyFacade>()
    .AddKafkaSubscribersHostedService<string, string>(builder.Configuration,
        kafkaActionHandlers: [
                KafkaHandlerSupplier<IMessageHandler, MyHandler>
        ]);

builder.Services.AddControllers()
                .UsePascalCase()
                .SuppressAutoACR();

var app = builder.Build();

// ** runtime configure part **
if (app.Environment.IsDevelopment())
    app.UseDeveloperExceptionPage();

app.UseSwaggerEndpoint(app.Services.GetRequiredService<IApiVersionDescriptionProvider>(), "IMAGE_CONTAINER_NAME");

app.UseRouting();

// Metrics middleware
app.UseAllMetricsMiddleware()
    .UseMiddleware<RequestMiddleware>();

app.UseAuthorization();

app.UseHealthChecks();
app.UseReadynessRoute();
app.MapMetrics();
app.MapControllers();

await app.RunAsync();