#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
ITE.AvailabilityRules
ITF.InfrastructureAndSettings
ITF.Lib.Common
ITF.SharedLibraries
ITF.SharedModels
ITI.AvailabilityRules
ITI2.AvailabilityRules
ITI.Availability.Library
ITI.SharedModels
ITP.AvailabilityRules
ITF.AvailabilityRules
ITS.AvailabilityRules
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  <NAME_EMAIL>:v3/interflorad365fo/$REPOSITORY/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-<NAME_EMAIL>:v3/interflorad365fo/$REPOSITORY/$REPOSITORY
  git -C $REPOSITORY pull
done
  
REPOSITORIESOCTO=(
IT.Microservices.ApiExtensionCT.Order
IT.Microservices.ApiExtensionCT.Product
IT.Microservices.Availability
IT.Microservices.CT.Customer.Synchronizer
IT.Microservices.CT.Order.Synchronizer
IT.Microservices.CT.Product.Synchronizer
IT.Microservices.Florist.Notification.Reactor
IT.Microservices.GFS.ProductExporter.Reactor
IT.Microservices.GFS.Supplier
IT.Microservices.LengowFeed.Api
IT.Microservices.NotificationSupplier
IT.Microservices.PFS.BackOffice
IT.Microservices.PFS.BackOffice.UnitTests
IT.Microservices.SequenceGenerator
ITS.Microservices.Azure.ProductReactor
ITS.Microservices.OrderSubset.Synchronizer
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  <NAME_EMAIL>:v3/OCTO-MS/$REPOSITORY/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-<NAME_EMAIL>:v3/OCTO-MS/$REPOSITORY/$REPOSITORY 
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done
