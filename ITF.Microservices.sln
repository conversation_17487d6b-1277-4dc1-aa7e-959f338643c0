Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{6955FF57-95B0-41E6-8087-B797ADD1009D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{AAF33B63-5DA4-4565-9162-A82D22947351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries.UnitTests", "src\ITF.SharedLibraries\tests\ITF.SharedLibraries.UnitTests\ITF.SharedLibraries.UnitTests.csproj", "{********-7CD4-491B-8CEB-806CC9A51D35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels.UnitTests", "src\ITF.SharedModels\tests\ITF.SharedModels.UnitTests\ITF.SharedModels.UnitTests.csproj", "{5DCD0871-E480-4B7E-A432-248A47D5C165}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.UnitTests", "src\ITF.Microservices.Availability\tests\ITF.Microservices.Availability.UnitTests\ITF.Microservices.Availability.UnitTests.csproj", "{0AE5F395-8665-45CE-846F-896F5476A375}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability", "src\ITF.Microservices.Availability\src\ITF.Microservices.Availability.csproj", "{F538B31D-22EB-4582-925B-A3A07D8AB639}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CatalogSynchronizer", "src\ITF.Microservices.CatalogSynchronizer\src\ITF.Microservices.CatalogSynchronizer.csproj", "{AA5C3A03-2753-4123-99D2-8E314F4A5F00}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CatalogSynchronizer.UnitTests", "src\ITF.Microservices.CatalogSynchronizer\tests\ITF.Microservices.CatalogSynchronizer.UnitTests\ITF.Microservices.CatalogSynchronizer.UnitTests.csproj", "{3993E394-85F8-41D0-93E9-A3F18523D1F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSynchronizer", "src\ITF.Microservices.FloristSynchronizer\src\ITF.Microservices.FloristSynchronizer.csproj", "{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedAssignmentRules", "src\ITF.SharedAssignmentRules\src\ITF.SharedAssignmentRules.csproj", "{7E2E9FC3-46A8-4785-A042-E10C2AADE047}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedAssignmentRules.UnitTests", "src\ITF.SharedAssignmentRules\tests\ITF.SharedAssignmentRules.UnitTests\ITF.SharedAssignmentRules.UnitTests.csproj", "{5511F41E-DCE6-463F-9902-338193BBF8BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyOrderEvents", "src\ITF.Microservices.LegacyOrderEvents\src\ITF.Microservices.LegacyOrderEvents.csproj", "{E99F4281-007C-400D-B927-2D9494C8EB89}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyOrderEvents.UnitTests", "src\ITF.Microservices.LegacyOrderEvents\tests\ITF.Microservices.LegacyOrderEvents.UnitTests\ITF.Microservices.LegacyOrderEvents.UnitTests.csproj", "{347FA710-17D7-48C6-BCAD-B1096F36C538}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FlashInfo", "src\ITF.Microservices.FlashInfo\src\ITF.Microservices.FlashInfo.csproj", "{A0FB90CC-5C90-4A6F-997F-E6E6FB08FD41}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FlashInfo.UnitTests", "src\ITF.Microservices.FlashInfo\tests\ITF.Microservices.FlashInfo.UnitTests\ITF.Microservices.FlashInfo.UnitTests.csproj", "{DA34D818-4030-426D-8B35-E98A435967F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyFloristEvents", "src\ITF.Microservices.LegacyFloristEvents\src\ITF.Microservices.LegacyFloristEvents.csproj", "{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITF.Microservices.Availability.CatalogSubset.Synchronizer\src\ITF.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITF.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITF.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITF.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{90C52F44-D72C-44EC-9902-654BF8D38585}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.FloristSubset.Synchronizer", "src\ITF.Microservices.Availability.FloristSubset.Synchronizer\src\ITF.Microservices.Availability.FloristSubset.Synchronizer.csproj", "{6F026BC9-B612-4794-B784-8DD105CA684C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.OrderSubset.Synchronizer", "src\ITF.Microservices.Availability.OrderSubset.Synchronizer\src\ITF.Microservices.Availability.OrderSubset.Synchronizer.csproj", "{F4BAF067-F4EF-460C-9B99-7B93854173A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.StaticSubset.Provider", "src\ITF.Microservices.Availability.StaticSubset.Provider\src\ITF.Microservices.Availability.StaticSubset.Provider.csproj", "{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer", "src\IT.Microservices.CT.Product.Synchronizer\src\IT.Microservices.CT.Product.Synchronizer.csproj", "{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer", "src\IT.Microservices.CT.Order.Synchronizer\src\IT.Microservices.CT.Order.Synchronizer.csproj", "{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Customer.Synchronizer", "src\IT.Microservices.CT.Customer.Synchronizer\src\IT.Microservices.CT.Customer.Synchronizer.csproj", "{E53EAE82-AB46-4330-A687-2D2312DFC5EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.GFS.Supplier", "src\IT.Microservices.GFS.Supplier\src\IT.Microservices.GFS.Supplier.csproj", "{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.GFS.Supplier.UnitTests", "src\IT.Microservices.GFS.Supplier\tests\IT.Microservices.GFS.Supplier.UnitTests\IT.Microservices.GFS.Supplier.UnitTests.csproj", "{369790E3-B571-4B65-BAC3-261788251B60}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer.UnitTests", "src\IT.Microservices.CT.Product.Synchronizer\tests\IT.Microservices.CT.Product.Synchronizer.UnitTests\IT.Microservices.CT.Product.Synchronizer.UnitTests.csproj", "{9A3B54D1-EB60-4205-A888-720449332CA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability", "src\IT.Microservices.Availability\src\IT.Microservices.Availability.csproj", "{0B09E6D6-058B-4701-8CDB-EB9591437ED2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability.UnitTests", "src\IT.Microservices.Availability\tests\IT.Microservices.Availability.UnitTests\IT.Microservices.Availability.UnitTests.csproj", "{53450071-5EB2-4544-9371-D5237ED99A1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common.UnitTests", "src\ITF.Lib.Common\tests\ITF.Lib.Common.UnitTests\ITF.Lib.Common.UnitTests.csproj", "{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer.UnitTests", "src\IT.Microservices.CT.Order.Synchronizer\tests\IT.Microservices.CT.Order.Synchronizer.UnitTests\IT.Microservices.CT.Order.Synchronizer.UnitTests.csproj", "{6A124125-1251-48D0-91B7-4EF563085A6B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyFloristEvents.UnitTests", "src\ITF.Microservices.LegacyFloristEvents\tests\ITF.Microservices.LegacyFloristEvents.UnitTests\ITF.Microservices.LegacyFloristEvents.UnitTests.csproj", "{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSettings", "src\ITF.Microservices.FloristSettings\src\ITF.Microservices.FloristSettings.csproj", "{1626B2FB-07BA-4D71-90B1-F552F16807FE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSettings.UnitTests", "src\ITF.Microservices.FloristSettings\tests\ITF.Microservices.FloristSettings.UnitTests\ITF.Microservices.FloristSettings.UnitTests.csproj", "{34F0C226-21A4-46FE-8F5C-A81EE982630A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CMSDraft.Synchronizer", "src\IT.Microservices.CMSDraft.Synchronizer\src\IT.Microservices.CMSDraft.Synchronizer.csproj", "{0B875DE9-435E-4C4F-AE62-1FB8C2732748}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CMSDraft.Synchronizer.UnitTests", "src\IT.Microservices.CMSDraft.Synchronizer\tests\IT.Microservices.CMSDraft.Synchronizer.UnitTests\IT.Microservices.CMSDraft.Synchronizer.UnitTests.csproj", "{EA8D9472-E868-4E8E-9B99-461827C6E0DF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.SequenceGenerator", "src\IT.Microservices.SequenceGenerator\src\IT.Microservices.SequenceGenerator.csproj", "{16C26C12-B1F0-4135-9EF0-4296125B35FE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.SequenceGenerator.UnitTests", "src\IT.Microservices.SequenceGenerator\tests\IT.Microservices.SequenceGenerator.UnitTests\IT.Microservices.SequenceGenerator.UnitTests.csproj", "{FFD4CCFC-4E85-4AD7-B3A8-8B6138B265E4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.AbandonedCartDetector", "src\IT.Microservices.AbandonedCartDetector\src\IT.Microservices.AbandonedCartDetector.csproj", "{2DE2B241-F6E5-493A-866D-1A806E95D5C7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.AbandonedCartEmailSender", "src\IT.Microservices.AbandonedCartEmailSender\src\IT.Microservices.AbandonedCartEmailSender.csproj", "{4F91C852-2ACF-4A18-A893-0B44D377D576}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.AbandonedCartEmailSender.UnitTests", "src\IT.Microservices.AbandonedCartEmailSender\tests\IT.Microservices.AbandonedCartEmailSender.UnitTests\IT.Microservices.AbandonedCartEmailSender.UnitTests.csproj", "{58C98677-71F7-4A3C-94B7-71327DB2B672}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.AbandonedCartDetector.UnitTests", "src\IT.Microservices.AbandonedCartDetector\tests\IT.Microservices.AbandonedCartDetector.UnitTests\IT.Microservices.AbandonedCartDetector.UnitTests.csproj", "{05DDA855-47BA-4582-8715-2B64B04E0162}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.LengowFeed.Api", "src\IT.Microservices.LengowFeed.Api\src\IT.Microservices.LengowFeed.Api.csproj", "{36CC706E-D6CF-46BD-83B1-EB95476A7B1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.LengowFeed.Api.UnitTests", "src\IT.Microservices.LengowFeed.Api\tests\IT.Microservices.LengowFeed.Api.UnitTests\IT.Microservices.LengowFeed.Api.UnitTests.csproj", "{1564744F-8B7D-4E27-8B4F-0DE9EE3B57AA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Availability.Library", "src\ITF.Availability.Library\src\ITF.Availability.Library.csproj", "{23472C4A-B806-4692-9F09-6CA7A62233A3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Availability.Library.UnitTests", "src\ITF.Availability.Library\tests\ITF.Availability.Library.UnitTests\ITF.Availability.Library.UnitTests.csproj", "{D56012A0-53C0-4A7D-BB18-358015DDFBB2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules", "src\ITE.AvailabilityRules\src\ITE.AvailabilityRules.csproj", "{60052657-3B4F-4C53-BC2E-68DD168929E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules", "src\ITP.AvailabilityRules\src\ITP.AvailabilityRules.csproj", "{CC0C0AE8-D6E1-4DAB-BE78-BEFDE94FA88B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AssignementRules", "src\ITF.AssignementRules\src\ITF.AssignementRules.csproj", "{35E822C1-05F0-4324-814F-1C2BE6B5BD42}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AssignementRules.UnitTests", "src\ITF.AssignementRules\tests\ITF.AssignementRules.UnitTests\ITF.AssignementRules.UnitTests.csproj", "{81B2546B-C4C8-41B2-A6B1-EE25E9DCB9F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CourrierWrapper", "src\ITF.Microservices.CourrierWrapper\src\ITF.Microservices.CourrierWrapper.csproj", "{BFC84C27-CC91-44D1-B0B0-B1B4A2BF09B3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CourrierWrapper.UnitTests", "src\ITF.Microservices.CourrierWrapper\tests\ITF.Microservices.CourrierWrapper.UnitTests\ITF.Microservices.CourrierWrapper.UnitTests.csproj", "{936B9DA3-9477-4112-91AB-F65D473463D2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CourrierPoller", "src\ITF.Microservices.CourrierPoller\src\ITF.Microservices.CourrierPoller.csproj", "{B7AE9539-E6B9-4FEC-AD1F-765A4BF9D518}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CourrierPoller.UnitTests", "src\ITF.Microservices.CourrierPoller\tests\ITF.Microservices.CourrierPoller.UnitTests\ITF.Microservices.CourrierPoller.UnitTests.csproj", "{64E0249A-EC18-45F5-ABAD-6637421BB287}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CourrierSynchronizer", "src\ITF.Microservices.CourrierSynchronizer\src\ITF.Microservices.CourrierSynchronizer.csproj", "{62E150FB-CAA9-4027-A70F-FFB1A419BB68}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CourrierSynchronizer.UnitTests", "src\ITF.Microservices.CourrierSynchronizer\tests\ITF.Microservices.CourrierSynchronizer.UnitTests\ITF.Microservices.CourrierSynchronizer.UnitTests.csproj", "{F8E9DC60-C239-4D84-92F4-705DB037BC85}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderConfirmationEmailSender", "src\IT.Microservices.OrderConfirmationEmailSender\src\IT.Microservices.OrderConfirmationEmailSender.csproj", "{8C6A6CB5-6321-4CAC-A2B9-B2391E128023}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderConfirmationEmailSender.UnitTests", "src\IT.Microservices.OrderConfirmationEmailSender\tests\IT.Microservices.OrderConfirmationEmailSender.UnitTests\IT.Microservices.OrderConfirmationEmailSender.UnitTests.csproj", "{4B61B327-1377-4A96-B3B2-587E662F2C2E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Customer.Synchronizer.UnitTests", "src\IT.Microservices.CT.Customer.Synchronizer\tests\IT.Microservices.CT.Customer.Synchronizer.UnitTests\IT.Microservices.CT.Customer.Synchronizer.UnitTests.csproj", "{804CB7B3-FA35-49D7-85AE-694235C90FA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Florist", "src\IT.Microservices.Florist\src\IT.Microservices.Florist.csproj", "{6AFE1AD3-2713-4BA8-9CD4-3717C5EDD090}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Florist.UnitTests", "src\IT.Microservices.Florist\tests\IT.Microservices.Florist.UnitTests\IT.Microservices.Florist.UnitTests.csproj", "{696CAB82-917B-4A28-BED4-F18637BA14B3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Florist.Queries", "src\IT.Microservices.Florist.Queries\src\IT.Microservices.Florist.Queries.csproj", "{A54B4697-64E5-4C3E-9BC8-6AD384C1E7A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Florist.Queries.UnitTests", "src\IT.Microservices.Florist.Queries\tests\IT.Microservices.Florist.Queries.UnitTests\IT.Microservices.Florist.Queries.UnitTests.csproj", "{C6FB932A-2F83-45C4-A68E-1E4438172A11}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules.UnitTests", "src\ITE.AvailabilityRules\tests\ITE.AvailabilityRules.UnitTests\ITE.AvailabilityRules.UnitTests.csproj", "{6165F346-36B1-4AF4-808B-5237BDBB9ECD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules.UnitTests", "src\ITP.AvailabilityRules\tests\ITP.AvailabilityRules.UnitTests\ITP.AvailabilityRules.UnitTests.csproj", "{AE974F98-E3D6-4A2C-AAE4-48764FA896E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITE.Microservices.Availability.CatalogSubset.Synchronizer\src\ITE.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{B07EEC96-CBDA-4CEF-8E6D-07FB4E2465CE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITE.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITE.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITE.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{DB0F1151-A093-4FA2-8ECE-0B12623399F6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.OrderSubset.Synchronizer", "src\ITE.Microservices.OrderSubset.Synchronizer\src\ITE.Microservices.OrderSubset.Synchronizer.csproj", "{E0D6B739-14AB-4912-8C1E-2012A681B233}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITE.Microservices.OrderSubset.Synchronizer\tests\ITE.Microservices.OrderSubset.Synchronizer.UnitTests\ITE.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{8AB7E774-86B5-482C-991A-0069D846741E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.ProductSubset.Synchronizer", "src\ITE.Microservices.ProductSubset.Synchronizer\src\ITE.Microservices.ProductSubset.Synchronizer.csproj", "{C51E2CF3-CDE5-440A-97BC-FA984A8602F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITE.Microservices.ProductSubset.Synchronizer\tests\ITE.Microservices.ProductSubset.Synchronizer.UnitTests\ITE.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{C59E6C7E-F50C-40B6-831D-68F2B94670BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.FloristSubset.Synchronizer.UnitTests", "src\ITF.Microservices.Availability.FloristSubset.Synchronizer\tests\ITF.Microservices.Availability.FloristSubset.Synchronizer.UnitTests\ITF.Microservices.Availability.FloristSubset.Synchronizer.UnitTests.csproj", "{A74C9EA6-34E6-4ADE-8030-D200FF335456}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.OrderSubset.Synchronizer.UnitTests", "src\ITF.Microservices.Availability.OrderSubset.Synchronizer\tests\ITF.Microservices.Availability.OrderSubset.Synchronizer.UnitTests\ITF.Microservices.Availability.OrderSubset.Synchronizer.UnitTests.csproj", "{6A732CF4-5F9F-4D82-87D3-970006DF2C4B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.StaticSubset.Provider.UnitTests", "src\ITF.Microservices.Availability.StaticSubset.Provider\tests\ITF.Microservices.Availability.StaticSubset.Provider.UnitTests\ITF.Microservices.Availability.StaticSubset.Provider.UnitTests.csproj", "{CF669A08-5344-49B8-A363-23484A1FC10B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.Reactor", "src\ITF.Microservices.Availability.Reactor\src\ITF.Microservices.Availability.Reactor.csproj", "{E730A156-3558-46E8-A442-9DBE6897909A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.Reactor.UnitTests", "src\ITF.Microservices.Availability.Reactor\tests\ITF.Microservices.Availability.Reactor.UnitTests\ITF.Microservices.Availability.Reactor.UnitTests.csproj", "{7047191A-F35D-441B-B29C-00B3A5850474}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSynchronizer.UnitTests", "src\ITF.Microservices.FloristSynchronizer\tests\ITF.Microservices.FloristSynchronizer.UnitTests\ITF.Microservices.FloristSynchronizer.UnitTests.csproj", "{4B658081-93BA-473D-B2A7-45C43CFFE024}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITP.Microservices.Availability.CatalogSubset.Synchronizer\src\ITP.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{7DC8D14B-7CED-4A3E-8480-C9F9A81CC34B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITP.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITP.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITP.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{9C74DE0B-FEB7-44E7-9BFC-4FF1AF8E3482}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.OrderSubset.Synchronizer", "src\ITP.Microservices.OrderSubset.Synchronizer\src\ITP.Microservices.OrderSubset.Synchronizer.csproj", "{FB451A96-1EDF-4D4B-A405-90A0A54B91A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITP.Microservices.OrderSubset.Synchronizer\tests\ITP.Microservices.OrderSubset.Synchronizer.UnitTests\ITP.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{98D204A7-1EC7-48DD-A4A8-7D9E4D03FF4A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.ProductSubset.Synchronizer", "src\ITP.Microservices.ProductSubset.Synchronizer\src\ITP.Microservices.ProductSubset.Synchronizer.csproj", "{AF236ECD-F0C2-4279-8241-3A0BA0A4F298}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITP.Microservices.ProductSubset.Synchronizer\tests\ITP.Microservices.ProductSubset.Synchronizer.UnitTests\ITP.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{C5C2E439-5C73-460C-9227-D3D5BB0BDF75}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITF.Microservices.OrderSubset.Synchronizer\tests\ITF.Microservices.OrderSubset.Synchronizer.UnitTests\ITF.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{2F14576A-CE96-4D88-9DC1-4E7CC322F1E5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules", "src\ITF.AvailabilityRules\src\ITF.AvailabilityRules.csproj", "{1EB864CB-4A69-49C0-8CD4-25977CE1EDF3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules.UnitTests", "src\ITF.AvailabilityRules\tests\ITF.AvailabilityRules.UnitTests\ITF.AvailabilityRules.UnitTests.csproj", "{619B56AF-F46E-46C8-A19B-A87545D2A618}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CRMFeeder", "src\IT.Microservices.CRMFeeder\src\IT.Microservices.CRMFeeder.csproj", "{A6780E55-8588-4B51-A4BE-79451A24E2B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CRMFeeder.UnitTests", "src\IT.Microservices.CRMFeeder\tests\IT.Microservices.CRMFeeder.UnitTests\IT.Microservices.CRMFeeder.UnitTests.csproj", "{45A91BF6-E0D8-44D7-B648-4391C8945D24}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Bloomreach.Reactor", "src\IT.Microservices.Bloomreach.Reactor\src\IT.Microservices.Bloomreach.Reactor.csproj", "{4A5DE184-CF07-44C1-AE39-379DC4B16FB6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Bloomreach.Sync", "src\IT.Microservices.Bloomreach.Sync\src\IT.Microservices.Bloomreach.Sync.csproj", "{E8397519-9205-42A2-9D1A-84CBB2F3A877}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Wrapper", "src\IT.Microservices.CT.Order.Wrapper\src\IT.Microservices.CT.Order.Wrapper.csproj", "{FD3AB7D1-C52A-495D-AE9A-80AF9C010462}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Wrapper.UnitTests", "src\IT.Microservices.CT.Order.Wrapper\tests\IT.Microservices.CT.Order.Wrapper.UnitTests\IT.Microservices.CT.Order.Wrapper.UnitTests.csproj", "{0E8E33C0-8F87-43C2-94A8-3BA4B98E2773}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.HttpToKafkaWrapper", "src\IT.Microservices.HttpToKafkaWrapper\src\IT.Microservices.HttpToKafkaWrapper.csproj", "{9C47CC1C-881C-4510-BE45-6F7E34B70684}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.HttpToKafkaWrapper.UnitTests", "src\IT.Microservices.HttpToKafkaWrapper\tests\IT.Microservices.HttpToKafkaWrapper.UnitTests\IT.Microservices.HttpToKafkaWrapper.UnitTests.csproj", "{9A4D8C88-B265-405A-980E-BA88070A22B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.EmailSender", "src\IT.Microservices.EmailSender\src\IT.Microservices.EmailSender.csproj", "{67D901BA-AC7D-4D96-9DED-9E66C9C0561A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.EmailSender.UnitTests", "src\IT.Microservices.EmailSender\tests\IT.Microservices.EmailSender.UnitTests\IT.Microservices.EmailSender.UnitTests.csproj", "{EEB5BC4B-17E0-45ED-9F99-0F19F37D7BF8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSubscription", "src\ITF.Microservices.FloristSubscription\src\ITF.Microservices.FloristSubscription.csproj", "{48B625A9-EF99-48FD-8E94-39AE457483EE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSubscription.UnitTests", "src\ITF.Microservices.FloristSubscription\tests\ITF.Microservices.FloristSubscription.UnitTests\ITF.Microservices.FloristSubscription.UnitTests.csproj", "{94C05CA0-1F41-44E4-8F66-D2D511087040}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderReactor", "src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj", "{BCFEBC8F-0A6D-48DA-B48B-0AD18FF7238D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderReactor.UnitTests", "src\IT.Microservices.OrderReactor\tests\IT.Microservices.OrderReactor.UnitTests\IT.Microservices.OrderReactor.UnitTests.csproj", "{FC5C1274-ED67-418C-9902-9650905667DB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT", "src\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj", "{35452F66-F97E-45C9-B213-72C23EA484C9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT.UnitTests", "src\IT.SharedLibraries.CT\tests\IT.SharedLibraries.CT.UnitTests\IT.SharedLibraries.CT.UnitTests.csproj", "{DEC27F5A-05D5-4B69-A74D-7FE8425F59D4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderLogHistoryReactor", "src\IT.Microservices.OrderLogHistoryReactor\src\IT.Microservices.OrderLogHistoryReactor.csproj", "{236F521E-178E-4110-AD62-BC9268B703E7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderLogHistoryReactor.UnitTests", "src\IT.Microservices.OrderLogHistoryReactor\tests\IT.Microservices.OrderLogHistoryReactor.UnitTests\IT.Microservices.OrderLogHistoryReactor.UnitTests.csproj", "{D9269D77-D795-418D-977B-3E1662F7B14F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.NotificationSupplier", "src\IT.Microservices.NotificationSupplier\src\IT.Microservices.NotificationSupplier.csproj", "{F6D7A71B-738F-43A4-A579-5202902E76CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Payment", "src\ITF.Microservices.Payment\src\ITF.Microservices.Payment.csproj", "{4E54A38B-009D-499F-BEE7-CBBB76C92C3C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CatalogSynchronizer.Reactor.UnitTests", "src\ITF.Microservices.CatalogSynchronizer.Reactor\tests\ITF.Microservices.CatalogSynchronizer.Reactor.UnitTests\ITF.Microservices.CatalogSynchronizer.Reactor.UnitTests.csproj", "{FD128708-3EB1-4EB2-B8EA-0C00BD367377}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CatalogSynchronizer.Reactor", "src\ITF.Microservices.CatalogSynchronizer.Reactor\src\ITF.Microservices.CatalogSynchronizer.Reactor.csproj", "{F2996AFC-A3F3-47F9-BF67-275A90347D72}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Order.Library", "src\ITE.Order.Library\src\ITE.Order.Library.csproj", "{A797466D-525D-470F-8DEF-E166B8FCB9B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Order.Library.UnitTests", "src\ITE.Order.Library\tests\ITE.Order.Library.UnitTests\ITE.Order.Library.UnitTests.csproj", "{C6DB2036-653D-4FC8-BDE9-9FCB404592E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Order.Library", "src\ITF.Order.Library\src\ITF.Order.Library.csproj", "{5A8A60BC-823F-4966-A8A9-5A0A579CF049}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Order.Library.UnitTests", "src\ITF.Order.Library\tests\ITF.Order.Library.UnitTests\ITF.Order.Library.UnitTests.csproj", "{84B58C2F-5EAC-429D-98DE-211136D5C303}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.OrderSubset.Synchronizer", "src\ITF.Microservices.OrderSubset.Synchronizer\src\ITF.Microservices.OrderSubset.Synchronizer.csproj", "{FDE0EC25-B545-40DA-BD43-F2AAE5D4EF77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.DiscountCodeSync", "src\ITF.Microservices.DiscountCodeSync\src\ITF.Microservices.DiscountCodeSync.csproj", "{9AE16322-AEB6-429F-BF68-D4E7E5433F96}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Selfcare", "src\ITF.Microservices.Selfcare\src\ITF.Microservices.Selfcare.csproj", "{5A024E46-2CC1-48BA-A939-BC82A0BC144E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Selfcare.UnitTests", "src\ITF.Microservices.Selfcare\tests\ITF.Microservices.Selfcare.UnitTests\ITF.Microservices.Selfcare.UnitTests.csproj", "{B17818E7-8D4E-447B-B52A-D3148AC3B710}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.EmailEmarsysSender", "src\IT.Microservices.EmailEmarsysSender\src\IT.Microservices.EmailEmarsysSender.csproj", "{987B824B-1DCB-48C8-8B0C-2CDBD1982E0F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.EmailEmarsys.Reactor", "src\IT.Microservices.EmailEmarsys.Reactor\src\IT.Microservices.EmailEmarsys.Reactor.csproj", "{05AE6240-7949-4C8F-A6DD-8F4A0A7DD616}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.AvailabilityRules", "src\ITS.AvailabilityRules\src\ITS.AvailabilityRules.csproj", "{59E685BA-ED0A-4D47-A454-865E189AD9CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.AvailabilityRules.UnitTests", "src\ITS.AvailabilityRules\tests\ITS.AvailabilityRules.UnitTests\ITS.AvailabilityRules.UnitTests.csproj", "{4813F544-5FD0-46D6-AC31-D0254157585E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules.UnitTests", "src\ITI2.AvailabilityRules\tests\ITI2.AvailabilityRules.UnitTests\ITI2.AvailabilityRules.UnitTests.csproj", "{8A3C4624-957C-46D1-957C-BFE2A598AC2B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.BusinessUnit.Synchronizer", "src\IT.Microservices.CT.BusinessUnit.Synchronizer\src\IT.Microservices.CT.BusinessUnit.Synchronizer.csproj", "{113A8C8B-3C1B-43B1-88E6-F479E6220248}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.DiscountCodeSync.UnitTests", "src\ITF.Microservices.DiscountCodeSync\tests\ITF.Microservices.DiscountCodeSync.UnitTests\ITF.Microservices.DiscountCodeSync.UnitTests.csproj", "{98C1A760-51EA-4A4B-AE8F-C0051166597D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.ApiExtensionCT.Order", "src\IT.Microservices.ApiExtensionCT.Order\src\IT.Microservices.ApiExtensionCT.Order.csproj", "{9A74381F-E884-489C-B2FE-C664DC8ABC18}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.ApiExtensionCT.Order.UnitTests", "src\IT.Microservices.ApiExtensionCT.Order\tests\IT.Microservices.ApiExtensionCT.Order.UnitTests\IT.Microservices.ApiExtensionCT.Order.UnitTests.csproj", "{E1744775-4B55-4683-B390-82EABE91B357}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ITF.Microservices.BillManagement", "src\ITF.Microservices.BillManagement\src\ITF.Microservices.BillManagement.csproj", "{1850F0B7-207E-4141-95BF-92EFA8170778}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ITF.Microservices.BillManagement.UnitTests", "src\ITF.Microservices.BillManagement\tests\ITF.Microservices.BillManagement.UnitTests\ITF.Microservices.BillManagement.UnitTests.csproj", "{A198F381-6AA9-423C-964A-3AFB375A5D10}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.Microservices.Azure.ProductReactor", "src\ITS.Microservices.Azure.ProductReactor\src\ITS.Microservices.Azure.ProductReactor.csproj", "{3AFC8B5E-0AF9-484A-BBF2-65BE386164EF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.Microservices.OrderSubset.Synchronizer", "src\ITS.Microservices.OrderSubset.Synchronizer\src\ITS.Microservices.OrderSubset.Synchronizer.csproj", "{3209505A-002C-479D-8312-BD60A93AF8F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.FloristReactor", "src\IT.Microservices.FloristReactor\src\IT.Microservices.FloristReactor.csproj", "{CEAAAD6F-1C90-4C94-AA70-3A127353D2B6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.FloristReactor.UnitTests", "src\IT.Microservices.FloristReactor\tests\IT.Microservices.FloristReactor.UnitTests\IT.Microservices.FloristReactor.UnitTests.csproj", "{DC2243AD-C877-4532-A1F2-5533E605822F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.OrderSubset.Synchronizer", "src\ITI2.Microservices.OrderSubset.Synchronizer\src\ITI2.Microservices.OrderSubset.Synchronizer.csproj", "{2A4D2462-4E3E-4F41-B9DA-E270D07EA892}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.OrderSubset.Synchronizer\tests\ITI2.Microservices.OrderSubset.Synchronizer.UnitTests\ITI2.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{FF042413-4827-4463-BEBE-D3C22075C111}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.AuthenticationApi", "src\IT.Microservices.AuthenticationApi\src\IT.Microservices.AuthenticationApi.csproj", "{1BC7F111-0850-4272-A510-C49A5E650603}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.AuthenticationApi.UnitTests", "src\IT.Microservices.AuthenticationApi\tests\IT.Microservices.AuthenticationApi.UnitTests\IT.Microservices.AuthenticationApi.UnitTests.csproj", "{A7A43689-94E4-4EDF-B513-B9460AACE82F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SE.CourierLib.Qapla", "src\SE.CourierLib.Qapla\src\SE.CourierLib.Qapla.csproj", "{3F643824-1DF5-4541-9E51-1DFC80F2F193}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SE.CourierLib.Qapla.UnitTests", "src\SE.CourierLib.Qapla\tests\SE.CourierLib.Qapla.UnitTests\SE.CourierLib.Qapla.UnitTests.csproj", "{AB9BAFDD-D26B-4758-BE34-F726B844034F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.CourierLib.Shared", "src\IT.CourierLib.Shared\src\IT.CourierLib.Shared.csproj", "{F7C5CB4B-32C5-4D26-B15F-C8AC992D4151}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.CourierLib.Shared.UnitTests", "src\IT.CourierLib.Shared\tests\IT.CourierLib.Shared.UnitTests\IT.CourierLib.Shared.UnitTests.csproj", "{A2EBC4D1-35FD-4A2D-9979-F3FAB180D44A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Shipment", "src\IT.Microservices.Shipment\src\IT.Microservices.Shipment.csproj", "{0507CB74-E7B8-456C-95DB-279478E351E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Shipment.UnitTests", "src\IT.Microservices.Shipment\tests\IT.Microservices.Shipment.UnitTests\IT.Microservices.Shipment.UnitTests.csproj", "{2C65325E-1CF0-40A0-9716-BB59A64542B2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer\src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{C8D3E38E-5DB8-4C6B-9831-9E30C179BF06}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{5F0D186D-24EC-4687-A293-B000D132601D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.ProductSubset.Synchronizer", "src\ITI2.Microservices.ProductSubset.Synchronizer\src\ITI2.Microservices.ProductSubset.Synchronizer.csproj", "{8114D602-6CE4-4C8C-89E8-DB38C774ADCF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.ProductSubset.Synchronizer\tests\ITI2.Microservices.ProductSubset.Synchronizer.UnitTests\ITI2.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{8CA2F6F6-50D0-4CED-89D0-18B1FF2AC959}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Cron.PfsPurge", "src\IT.Microservices.Cron.PfsPurge\src\IT.Microservices.Cron.PfsPurge.csproj", "{909F646A-05CA-45D2-8C8E-1846026837F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Cron.PfsPurge.UnitTests", "src\IT.Microservices.Cron.PfsPurge\tests\IT.Microservices.Cron.PfsPurge.UnitTests\IT.Microservices.Cron.PfsPurge.UnitTests.csproj", "{978606CA-1C76-4C2C-978B-967655AD8453}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.CT.BusinessUnit.Synchronizer.UnitTests", "src\IT.Microservices.CT.BusinessUnit.Synchronizer\tests\IT.Microservices.CT.BusinessUnit.Synchronizer.UnitTests\IT.Microservices.CT.BusinessUnit.Synchronizer.UnitTests.csproj", "{ACE344AE-5366-77FF-AE8C-902DF75FBBEC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.PFSKpi", "src\IT.Microservices.PFSKpi\src\IT.Microservices.PFSKpi.csproj", "{51D1016C-3A56-69D9-34D5-E5674ECFFC58}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.PFSKpiSync", "src\IT.Microservices.PFSKpiSync\src\IT.Microservices.PFSKpiSync.csproj", "{144232ED-BB04-BC94-7E87-0B73343C51B7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.OccasionReminders", "src\IT.Microservices.OccasionReminders\src\IT.Microservices.OccasionReminders.csproj", "{AA746F00-4ADE-344D-7363-3AA4502781AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.CourrierWrapper", "src\IT.Microservices.CourrierWrapper\src\IT.Microservices.CourrierWrapper.csproj", "{FB908F45-6ABA-5962-B11E-3171BEDF6188}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SE.CourierLib.GLS", "src\SE.CourierLib.GLS\src\SE.CourierLib.GLS.csproj", "{E7CA5021-C7B4-BFD4-2E03-BAA53B8A0087}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SE.CourierLib.GLS.UnitTests", "src\SE.CourierLib.GLS\tests\SE.CourierLib.GLS.UnitTests\SE.CourierLib.GLS.UnitTests.csproj", "{7A5DE779-E32D-F53E-D465-6FCF26DA9633}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ITI2.AvailabilityRules", "src\ITI2.AvailabilityRules\src\ITI2.AvailabilityRules.csproj", "{6FCABDC6-2CE1-5D17-BD7D-E6DA915BB74F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.FileUploader", "src\IT.Microservices.FileUploader\src\IT.Microservices.FileUploader.csproj", "{3DE48537-3C3D-2E2E-CC29-9B96446E2C79}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.FileUploader.UnitTests", "src\IT.Microservices.FileUploader\tests\IT.Microservices.FileUploader.UnitTests\IT.Microservices.FileUploader.UnitTests.csproj", "{1CCA0BBC-98C9-9A24-9F05-704F1DB415ED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.EmarsysBatchSender", "src\IT.Microservices.EmarsysBatchSender\src\IT.Microservices.EmarsysBatchSender.csproj", "{9001C3FA-9E36-1FC2-CF5E-689B5E4484AE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IT.Microservices.EmarsysBatchSender.UnitTests", "src\IT.Microservices.EmarsysBatchSender\tests\IT.Microservices.EmarsysBatchSender.UnitTests\IT.Microservices.EmarsysBatchSender.UnitTests.csproj", "{2F494372-3BE5-41B6-3042-EEE60B65BB2F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.Build.0 = Release|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Release|Any CPU.Build.0 = Release|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Release|Any CPU.Build.0 = Release|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Release|Any CPU.Build.0 = Release|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Release|Any CPU.Build.0 = Release|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0FB90CC-5C90-4A6F-997F-E6E6FB08FD41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0FB90CC-5C90-4A6F-997F-E6E6FB08FD41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0FB90CC-5C90-4A6F-997F-E6E6FB08FD41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0FB90CC-5C90-4A6F-997F-E6E6FB08FD41}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA34D818-4030-426D-8B35-E98A435967F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA34D818-4030-426D-8B35-E98A435967F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA34D818-4030-426D-8B35-E98A435967F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA34D818-4030-426D-8B35-E98A435967F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Release|Any CPU.Build.0 = Release|Any CPU
		{E53EAE82-AB46-4330-A687-2D2312DFC5EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E53EAE82-AB46-4330-A687-2D2312DFC5EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E53EAE82-AB46-4330-A687-2D2312DFC5EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E53EAE82-AB46-4330-A687-2D2312DFC5EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Release|Any CPU.Build.0 = Release|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{1626B2FB-07BA-4D71-90B1-F552F16807FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1626B2FB-07BA-4D71-90B1-F552F16807FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1626B2FB-07BA-4D71-90B1-F552F16807FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1626B2FB-07BA-4D71-90B1-F552F16807FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{34F0C226-21A4-46FE-8F5C-A81EE982630A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34F0C226-21A4-46FE-8F5C-A81EE982630A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34F0C226-21A4-46FE-8F5C-A81EE982630A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34F0C226-21A4-46FE-8F5C-A81EE982630A}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B875DE9-435E-4C4F-AE62-1FB8C2732748}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B875DE9-435E-4C4F-AE62-1FB8C2732748}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B875DE9-435E-4C4F-AE62-1FB8C2732748}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B875DE9-435E-4C4F-AE62-1FB8C2732748}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA8D9472-E868-4E8E-9B99-461827C6E0DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA8D9472-E868-4E8E-9B99-461827C6E0DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA8D9472-E868-4E8E-9B99-461827C6E0DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA8D9472-E868-4E8E-9B99-461827C6E0DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{16C26C12-B1F0-4135-9EF0-4296125B35FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16C26C12-B1F0-4135-9EF0-4296125B35FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16C26C12-B1F0-4135-9EF0-4296125B35FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16C26C12-B1F0-4135-9EF0-4296125B35FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFD4CCFC-4E85-4AD7-B3A8-8B6138B265E4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFD4CCFC-4E85-4AD7-B3A8-8B6138B265E4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFD4CCFC-4E85-4AD7-B3A8-8B6138B265E4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFD4CCFC-4E85-4AD7-B3A8-8B6138B265E4}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DE2B241-F6E5-493A-866D-1A806E95D5C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DE2B241-F6E5-493A-866D-1A806E95D5C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2DE2B241-F6E5-493A-866D-1A806E95D5C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2DE2B241-F6E5-493A-866D-1A806E95D5C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F91C852-2ACF-4A18-A893-0B44D377D576}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F91C852-2ACF-4A18-A893-0B44D377D576}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F91C852-2ACF-4A18-A893-0B44D377D576}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F91C852-2ACF-4A18-A893-0B44D377D576}.Release|Any CPU.Build.0 = Release|Any CPU
		{58C98677-71F7-4A3C-94B7-71327DB2B672}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58C98677-71F7-4A3C-94B7-71327DB2B672}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58C98677-71F7-4A3C-94B7-71327DB2B672}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58C98677-71F7-4A3C-94B7-71327DB2B672}.Release|Any CPU.Build.0 = Release|Any CPU
		{05DDA855-47BA-4582-8715-2B64B04E0162}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05DDA855-47BA-4582-8715-2B64B04E0162}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05DDA855-47BA-4582-8715-2B64B04E0162}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05DDA855-47BA-4582-8715-2B64B04E0162}.Release|Any CPU.Build.0 = Release|Any CPU
		{36CC706E-D6CF-46BD-83B1-EB95476A7B1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36CC706E-D6CF-46BD-83B1-EB95476A7B1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36CC706E-D6CF-46BD-83B1-EB95476A7B1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36CC706E-D6CF-46BD-83B1-EB95476A7B1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{1564744F-8B7D-4E27-8B4F-0DE9EE3B57AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1564744F-8B7D-4E27-8B4F-0DE9EE3B57AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1564744F-8B7D-4E27-8B4F-0DE9EE3B57AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1564744F-8B7D-4E27-8B4F-0DE9EE3B57AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Release|Any CPU.Build.0 = Release|Any CPU
		{60052657-3B4F-4C53-BC2E-68DD168929E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60052657-3B4F-4C53-BC2E-68DD168929E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60052657-3B4F-4C53-BC2E-68DD168929E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60052657-3B4F-4C53-BC2E-68DD168929E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC0C0AE8-D6E1-4DAB-BE78-BEFDE94FA88B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC0C0AE8-D6E1-4DAB-BE78-BEFDE94FA88B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC0C0AE8-D6E1-4DAB-BE78-BEFDE94FA88B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC0C0AE8-D6E1-4DAB-BE78-BEFDE94FA88B}.Release|Any CPU.Build.0 = Release|Any CPU
		{35E822C1-05F0-4324-814F-1C2BE6B5BD42}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35E822C1-05F0-4324-814F-1C2BE6B5BD42}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35E822C1-05F0-4324-814F-1C2BE6B5BD42}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35E822C1-05F0-4324-814F-1C2BE6B5BD42}.Release|Any CPU.Build.0 = Release|Any CPU
		{81B2546B-C4C8-41B2-A6B1-EE25E9DCB9F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81B2546B-C4C8-41B2-A6B1-EE25E9DCB9F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81B2546B-C4C8-41B2-A6B1-EE25E9DCB9F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81B2546B-C4C8-41B2-A6B1-EE25E9DCB9F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{BFC84C27-CC91-44D1-B0B0-B1B4A2BF09B3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BFC84C27-CC91-44D1-B0B0-B1B4A2BF09B3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BFC84C27-CC91-44D1-B0B0-B1B4A2BF09B3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BFC84C27-CC91-44D1-B0B0-B1B4A2BF09B3}.Release|Any CPU.Build.0 = Release|Any CPU
		{936B9DA3-9477-4112-91AB-F65D473463D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{936B9DA3-9477-4112-91AB-F65D473463D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{936B9DA3-9477-4112-91AB-F65D473463D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{936B9DA3-9477-4112-91AB-F65D473463D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{B7AE9539-E6B9-4FEC-AD1F-765A4BF9D518}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B7AE9539-E6B9-4FEC-AD1F-765A4BF9D518}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B7AE9539-E6B9-4FEC-AD1F-765A4BF9D518}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B7AE9539-E6B9-4FEC-AD1F-765A4BF9D518}.Release|Any CPU.Build.0 = Release|Any CPU
		{64E0249A-EC18-45F5-ABAD-6637421BB287}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64E0249A-EC18-45F5-ABAD-6637421BB287}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64E0249A-EC18-45F5-ABAD-6637421BB287}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64E0249A-EC18-45F5-ABAD-6637421BB287}.Release|Any CPU.Build.0 = Release|Any CPU
		{62E150FB-CAA9-4027-A70F-FFB1A419BB68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{62E150FB-CAA9-4027-A70F-FFB1A419BB68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{62E150FB-CAA9-4027-A70F-FFB1A419BB68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{62E150FB-CAA9-4027-A70F-FFB1A419BB68}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8E9DC60-C239-4D84-92F4-705DB037BC85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8E9DC60-C239-4D84-92F4-705DB037BC85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8E9DC60-C239-4D84-92F4-705DB037BC85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8E9DC60-C239-4D84-92F4-705DB037BC85}.Release|Any CPU.Build.0 = Release|Any CPU
		{8C6A6CB5-6321-4CAC-A2B9-B2391E128023}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8C6A6CB5-6321-4CAC-A2B9-B2391E128023}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8C6A6CB5-6321-4CAC-A2B9-B2391E128023}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8C6A6CB5-6321-4CAC-A2B9-B2391E128023}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B61B327-1377-4A96-B3B2-587E662F2C2E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B61B327-1377-4A96-B3B2-587E662F2C2E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B61B327-1377-4A96-B3B2-587E662F2C2E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B61B327-1377-4A96-B3B2-587E662F2C2E}.Release|Any CPU.Build.0 = Release|Any CPU
		{804CB7B3-FA35-49D7-85AE-694235C90FA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{804CB7B3-FA35-49D7-85AE-694235C90FA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{804CB7B3-FA35-49D7-85AE-694235C90FA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{804CB7B3-FA35-49D7-85AE-694235C90FA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{6AFE1AD3-2713-4BA8-9CD4-3717C5EDD090}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6AFE1AD3-2713-4BA8-9CD4-3717C5EDD090}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6AFE1AD3-2713-4BA8-9CD4-3717C5EDD090}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6AFE1AD3-2713-4BA8-9CD4-3717C5EDD090}.Release|Any CPU.Build.0 = Release|Any CPU
		{696CAB82-917B-4A28-BED4-F18637BA14B3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{696CAB82-917B-4A28-BED4-F18637BA14B3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{696CAB82-917B-4A28-BED4-F18637BA14B3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{696CAB82-917B-4A28-BED4-F18637BA14B3}.Release|Any CPU.Build.0 = Release|Any CPU
		{A54B4697-64E5-4C3E-9BC8-6AD384C1E7A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A54B4697-64E5-4C3E-9BC8-6AD384C1E7A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A54B4697-64E5-4C3E-9BC8-6AD384C1E7A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A54B4697-64E5-4C3E-9BC8-6AD384C1E7A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6FB932A-2F83-45C4-A68E-1E4438172A11}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6FB932A-2F83-45C4-A68E-1E4438172A11}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6FB932A-2F83-45C4-A68E-1E4438172A11}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6FB932A-2F83-45C4-A68E-1E4438172A11}.Release|Any CPU.Build.0 = Release|Any CPU
		{6165F346-36B1-4AF4-808B-5237BDBB9ECD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6165F346-36B1-4AF4-808B-5237BDBB9ECD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6165F346-36B1-4AF4-808B-5237BDBB9ECD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6165F346-36B1-4AF4-808B-5237BDBB9ECD}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE974F98-E3D6-4A2C-AAE4-48764FA896E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE974F98-E3D6-4A2C-AAE4-48764FA896E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE974F98-E3D6-4A2C-AAE4-48764FA896E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE974F98-E3D6-4A2C-AAE4-48764FA896E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{B07EEC96-CBDA-4CEF-8E6D-07FB4E2465CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B07EEC96-CBDA-4CEF-8E6D-07FB4E2465CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B07EEC96-CBDA-4CEF-8E6D-07FB4E2465CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B07EEC96-CBDA-4CEF-8E6D-07FB4E2465CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB0F1151-A093-4FA2-8ECE-0B12623399F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB0F1151-A093-4FA2-8ECE-0B12623399F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB0F1151-A093-4FA2-8ECE-0B12623399F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB0F1151-A093-4FA2-8ECE-0B12623399F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0D6B739-14AB-4912-8C1E-2012A681B233}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0D6B739-14AB-4912-8C1E-2012A681B233}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0D6B739-14AB-4912-8C1E-2012A681B233}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0D6B739-14AB-4912-8C1E-2012A681B233}.Release|Any CPU.Build.0 = Release|Any CPU
		{8AB7E774-86B5-482C-991A-0069D846741E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8AB7E774-86B5-482C-991A-0069D846741E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8AB7E774-86B5-482C-991A-0069D846741E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8AB7E774-86B5-482C-991A-0069D846741E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C51E2CF3-CDE5-440A-97BC-FA984A8602F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C51E2CF3-CDE5-440A-97BC-FA984A8602F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C51E2CF3-CDE5-440A-97BC-FA984A8602F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C51E2CF3-CDE5-440A-97BC-FA984A8602F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{C59E6C7E-F50C-40B6-831D-68F2B94670BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C59E6C7E-F50C-40B6-831D-68F2B94670BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C59E6C7E-F50C-40B6-831D-68F2B94670BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C59E6C7E-F50C-40B6-831D-68F2B94670BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{A74C9EA6-34E6-4ADE-8030-D200FF335456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A74C9EA6-34E6-4ADE-8030-D200FF335456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A74C9EA6-34E6-4ADE-8030-D200FF335456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A74C9EA6-34E6-4ADE-8030-D200FF335456}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A732CF4-5F9F-4D82-87D3-970006DF2C4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A732CF4-5F9F-4D82-87D3-970006DF2C4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A732CF4-5F9F-4D82-87D3-970006DF2C4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A732CF4-5F9F-4D82-87D3-970006DF2C4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{CF669A08-5344-49B8-A363-23484A1FC10B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CF669A08-5344-49B8-A363-23484A1FC10B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CF669A08-5344-49B8-A363-23484A1FC10B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CF669A08-5344-49B8-A363-23484A1FC10B}.Release|Any CPU.Build.0 = Release|Any CPU
		{E730A156-3558-46E8-A442-9DBE6897909A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E730A156-3558-46E8-A442-9DBE6897909A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E730A156-3558-46E8-A442-9DBE6897909A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E730A156-3558-46E8-A442-9DBE6897909A}.Release|Any CPU.Build.0 = Release|Any CPU
		{7047191A-F35D-441B-B29C-00B3A5850474}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7047191A-F35D-441B-B29C-00B3A5850474}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7047191A-F35D-441B-B29C-00B3A5850474}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7047191A-F35D-441B-B29C-00B3A5850474}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B658081-93BA-473D-B2A7-45C43CFFE024}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B658081-93BA-473D-B2A7-45C43CFFE024}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B658081-93BA-473D-B2A7-45C43CFFE024}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B658081-93BA-473D-B2A7-45C43CFFE024}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DC8D14B-7CED-4A3E-8480-C9F9A81CC34B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DC8D14B-7CED-4A3E-8480-C9F9A81CC34B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DC8D14B-7CED-4A3E-8480-C9F9A81CC34B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DC8D14B-7CED-4A3E-8480-C9F9A81CC34B}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C74DE0B-FEB7-44E7-9BFC-4FF1AF8E3482}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C74DE0B-FEB7-44E7-9BFC-4FF1AF8E3482}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C74DE0B-FEB7-44E7-9BFC-4FF1AF8E3482}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C74DE0B-FEB7-44E7-9BFC-4FF1AF8E3482}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB451A96-1EDF-4D4B-A405-90A0A54B91A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB451A96-1EDF-4D4B-A405-90A0A54B91A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB451A96-1EDF-4D4B-A405-90A0A54B91A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB451A96-1EDF-4D4B-A405-90A0A54B91A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{98D204A7-1EC7-48DD-A4A8-7D9E4D03FF4A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98D204A7-1EC7-48DD-A4A8-7D9E4D03FF4A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98D204A7-1EC7-48DD-A4A8-7D9E4D03FF4A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98D204A7-1EC7-48DD-A4A8-7D9E4D03FF4A}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF236ECD-F0C2-4279-8241-3A0BA0A4F298}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF236ECD-F0C2-4279-8241-3A0BA0A4F298}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF236ECD-F0C2-4279-8241-3A0BA0A4F298}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF236ECD-F0C2-4279-8241-3A0BA0A4F298}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5C2E439-5C73-460C-9227-D3D5BB0BDF75}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5C2E439-5C73-460C-9227-D3D5BB0BDF75}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5C2E439-5C73-460C-9227-D3D5BB0BDF75}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5C2E439-5C73-460C-9227-D3D5BB0BDF75}.Release|Any CPU.Build.0 = Release|Any CPU
		{2F14576A-CE96-4D88-9DC1-4E7CC322F1E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2F14576A-CE96-4D88-9DC1-4E7CC322F1E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2F14576A-CE96-4D88-9DC1-4E7CC322F1E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2F14576A-CE96-4D88-9DC1-4E7CC322F1E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{1EB864CB-4A69-49C0-8CD4-25977CE1EDF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1EB864CB-4A69-49C0-8CD4-25977CE1EDF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1EB864CB-4A69-49C0-8CD4-25977CE1EDF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1EB864CB-4A69-49C0-8CD4-25977CE1EDF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{619B56AF-F46E-46C8-A19B-A87545D2A618}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{619B56AF-F46E-46C8-A19B-A87545D2A618}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{619B56AF-F46E-46C8-A19B-A87545D2A618}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{619B56AF-F46E-46C8-A19B-A87545D2A618}.Release|Any CPU.Build.0 = Release|Any CPU
		{A6780E55-8588-4B51-A4BE-79451A24E2B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A6780E55-8588-4B51-A4BE-79451A24E2B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A6780E55-8588-4B51-A4BE-79451A24E2B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A6780E55-8588-4B51-A4BE-79451A24E2B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{45A91BF6-E0D8-44D7-B648-4391C8945D24}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45A91BF6-E0D8-44D7-B648-4391C8945D24}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45A91BF6-E0D8-44D7-B648-4391C8945D24}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45A91BF6-E0D8-44D7-B648-4391C8945D24}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A5DE184-CF07-44C1-AE39-379DC4B16FB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A5DE184-CF07-44C1-AE39-379DC4B16FB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A5DE184-CF07-44C1-AE39-379DC4B16FB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A5DE184-CF07-44C1-AE39-379DC4B16FB6}.Release|Any CPU.Build.0 = Release|Any CPU
		{E8397519-9205-42A2-9D1A-84CBB2F3A877}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E8397519-9205-42A2-9D1A-84CBB2F3A877}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E8397519-9205-42A2-9D1A-84CBB2F3A877}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E8397519-9205-42A2-9D1A-84CBB2F3A877}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD3AB7D1-C52A-495D-AE9A-80AF9C010462}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD3AB7D1-C52A-495D-AE9A-80AF9C010462}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD3AB7D1-C52A-495D-AE9A-80AF9C010462}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD3AB7D1-C52A-495D-AE9A-80AF9C010462}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E8E33C0-8F87-43C2-94A8-3BA4B98E2773}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E8E33C0-8F87-43C2-94A8-3BA4B98E2773}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E8E33C0-8F87-43C2-94A8-3BA4B98E2773}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E8E33C0-8F87-43C2-94A8-3BA4B98E2773}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C47CC1C-881C-4510-BE45-6F7E34B70684}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C47CC1C-881C-4510-BE45-6F7E34B70684}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C47CC1C-881C-4510-BE45-6F7E34B70684}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C47CC1C-881C-4510-BE45-6F7E34B70684}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A4D8C88-B265-405A-980E-BA88070A22B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A4D8C88-B265-405A-980E-BA88070A22B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A4D8C88-B265-405A-980E-BA88070A22B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A4D8C88-B265-405A-980E-BA88070A22B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{67D901BA-AC7D-4D96-9DED-9E66C9C0561A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67D901BA-AC7D-4D96-9DED-9E66C9C0561A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67D901BA-AC7D-4D96-9DED-9E66C9C0561A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67D901BA-AC7D-4D96-9DED-9E66C9C0561A}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEB5BC4B-17E0-45ED-9F99-0F19F37D7BF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEB5BC4B-17E0-45ED-9F99-0F19F37D7BF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEB5BC4B-17E0-45ED-9F99-0F19F37D7BF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEB5BC4B-17E0-45ED-9F99-0F19F37D7BF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{48B625A9-EF99-48FD-8E94-39AE457483EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48B625A9-EF99-48FD-8E94-39AE457483EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48B625A9-EF99-48FD-8E94-39AE457483EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48B625A9-EF99-48FD-8E94-39AE457483EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{94C05CA0-1F41-44E4-8F66-D2D511087040}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94C05CA0-1F41-44E4-8F66-D2D511087040}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94C05CA0-1F41-44E4-8F66-D2D511087040}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94C05CA0-1F41-44E4-8F66-D2D511087040}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCFEBC8F-0A6D-48DA-B48B-0AD18FF7238D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCFEBC8F-0A6D-48DA-B48B-0AD18FF7238D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCFEBC8F-0A6D-48DA-B48B-0AD18FF7238D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCFEBC8F-0A6D-48DA-B48B-0AD18FF7238D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC5C1274-ED67-418C-9902-9650905667DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC5C1274-ED67-418C-9902-9650905667DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC5C1274-ED67-418C-9902-9650905667DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC5C1274-ED67-418C-9902-9650905667DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{35452F66-F97E-45C9-B213-72C23EA484C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35452F66-F97E-45C9-B213-72C23EA484C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35452F66-F97E-45C9-B213-72C23EA484C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35452F66-F97E-45C9-B213-72C23EA484C9}.Release|Any CPU.Build.0 = Release|Any CPU
		{DEC27F5A-05D5-4B69-A74D-7FE8425F59D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DEC27F5A-05D5-4B69-A74D-7FE8425F59D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DEC27F5A-05D5-4B69-A74D-7FE8425F59D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DEC27F5A-05D5-4B69-A74D-7FE8425F59D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{236F521E-178E-4110-AD62-BC9268B703E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{236F521E-178E-4110-AD62-BC9268B703E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{236F521E-178E-4110-AD62-BC9268B703E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{236F521E-178E-4110-AD62-BC9268B703E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{D9269D77-D795-418D-977B-3E1662F7B14F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D9269D77-D795-418D-977B-3E1662F7B14F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D9269D77-D795-418D-977B-3E1662F7B14F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D9269D77-D795-418D-977B-3E1662F7B14F}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6D7A71B-738F-43A4-A579-5202902E76CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6D7A71B-738F-43A4-A579-5202902E76CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6D7A71B-738F-43A4-A579-5202902E76CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6D7A71B-738F-43A4-A579-5202902E76CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{4E54A38B-009D-499F-BEE7-CBBB76C92C3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E54A38B-009D-499F-BEE7-CBBB76C92C3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E54A38B-009D-499F-BEE7-CBBB76C92C3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E54A38B-009D-499F-BEE7-CBBB76C92C3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD128708-3EB1-4EB2-B8EA-0C00BD367377}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD128708-3EB1-4EB2-B8EA-0C00BD367377}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD128708-3EB1-4EB2-B8EA-0C00BD367377}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD128708-3EB1-4EB2-B8EA-0C00BD367377}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2996AFC-A3F3-47F9-BF67-275A90347D72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2996AFC-A3F3-47F9-BF67-275A90347D72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2996AFC-A3F3-47F9-BF67-275A90347D72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2996AFC-A3F3-47F9-BF67-275A90347D72}.Release|Any CPU.Build.0 = Release|Any CPU
		{A797466D-525D-470F-8DEF-E166B8FCB9B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A797466D-525D-470F-8DEF-E166B8FCB9B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A797466D-525D-470F-8DEF-E166B8FCB9B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A797466D-525D-470F-8DEF-E166B8FCB9B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6DB2036-653D-4FC8-BDE9-9FCB404592E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6DB2036-653D-4FC8-BDE9-9FCB404592E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6DB2036-653D-4FC8-BDE9-9FCB404592E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6DB2036-653D-4FC8-BDE9-9FCB404592E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A8A60BC-823F-4966-A8A9-5A0A579CF049}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A8A60BC-823F-4966-A8A9-5A0A579CF049}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A8A60BC-823F-4966-A8A9-5A0A579CF049}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A8A60BC-823F-4966-A8A9-5A0A579CF049}.Release|Any CPU.Build.0 = Release|Any CPU
		{84B58C2F-5EAC-429D-98DE-211136D5C303}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84B58C2F-5EAC-429D-98DE-211136D5C303}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84B58C2F-5EAC-429D-98DE-211136D5C303}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84B58C2F-5EAC-429D-98DE-211136D5C303}.Release|Any CPU.Build.0 = Release|Any CPU
		{FDE0EC25-B545-40DA-BD43-F2AAE5D4EF77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDE0EC25-B545-40DA-BD43-F2AAE5D4EF77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FDE0EC25-B545-40DA-BD43-F2AAE5D4EF77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FDE0EC25-B545-40DA-BD43-F2AAE5D4EF77}.Release|Any CPU.Build.0 = Release|Any CPU
		{9AE16322-AEB6-429F-BF68-D4E7E5433F96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9AE16322-AEB6-429F-BF68-D4E7E5433F96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9AE16322-AEB6-429F-BF68-D4E7E5433F96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9AE16322-AEB6-429F-BF68-D4E7E5433F96}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A024E46-2CC1-48BA-A939-BC82A0BC144E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A024E46-2CC1-48BA-A939-BC82A0BC144E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A024E46-2CC1-48BA-A939-BC82A0BC144E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A024E46-2CC1-48BA-A939-BC82A0BC144E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B17818E7-8D4E-447B-B52A-D3148AC3B710}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B17818E7-8D4E-447B-B52A-D3148AC3B710}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B17818E7-8D4E-447B-B52A-D3148AC3B710}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B17818E7-8D4E-447B-B52A-D3148AC3B710}.Release|Any CPU.Build.0 = Release|Any CPU
		{987B824B-1DCB-48C8-8B0C-2CDBD1982E0F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{987B824B-1DCB-48C8-8B0C-2CDBD1982E0F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{987B824B-1DCB-48C8-8B0C-2CDBD1982E0F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{987B824B-1DCB-48C8-8B0C-2CDBD1982E0F}.Release|Any CPU.Build.0 = Release|Any CPU
		{05AE6240-7949-4C8F-A6DD-8F4A0A7DD616}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05AE6240-7949-4C8F-A6DD-8F4A0A7DD616}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05AE6240-7949-4C8F-A6DD-8F4A0A7DD616}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05AE6240-7949-4C8F-A6DD-8F4A0A7DD616}.Release|Any CPU.Build.0 = Release|Any CPU
		{59E685BA-ED0A-4D47-A454-865E189AD9CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59E685BA-ED0A-4D47-A454-865E189AD9CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59E685BA-ED0A-4D47-A454-865E189AD9CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59E685BA-ED0A-4D47-A454-865E189AD9CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{4813F544-5FD0-46D6-AC31-D0254157585E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4813F544-5FD0-46D6-AC31-D0254157585E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4813F544-5FD0-46D6-AC31-D0254157585E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4813F544-5FD0-46D6-AC31-D0254157585E}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A3C4624-957C-46D1-957C-BFE2A598AC2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A3C4624-957C-46D1-957C-BFE2A598AC2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A3C4624-957C-46D1-957C-BFE2A598AC2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A3C4624-957C-46D1-957C-BFE2A598AC2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{113A8C8B-3C1B-43B1-88E6-F479E6220248}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{113A8C8B-3C1B-43B1-88E6-F479E6220248}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{113A8C8B-3C1B-43B1-88E6-F479E6220248}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{113A8C8B-3C1B-43B1-88E6-F479E6220248}.Release|Any CPU.Build.0 = Release|Any CPU
		{98C1A760-51EA-4A4B-AE8F-C0051166597D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98C1A760-51EA-4A4B-AE8F-C0051166597D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98C1A760-51EA-4A4B-AE8F-C0051166597D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98C1A760-51EA-4A4B-AE8F-C0051166597D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A74381F-E884-489C-B2FE-C664DC8ABC18}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A74381F-E884-489C-B2FE-C664DC8ABC18}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A74381F-E884-489C-B2FE-C664DC8ABC18}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A74381F-E884-489C-B2FE-C664DC8ABC18}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1744775-4B55-4683-B390-82EABE91B357}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1744775-4B55-4683-B390-82EABE91B357}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1744775-4B55-4683-B390-82EABE91B357}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1744775-4B55-4683-B390-82EABE91B357}.Release|Any CPU.Build.0 = Release|Any CPU
		{1850F0B7-207E-4141-95BF-92EFA8170778}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1850F0B7-207E-4141-95BF-92EFA8170778}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1850F0B7-207E-4141-95BF-92EFA8170778}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1850F0B7-207E-4141-95BF-92EFA8170778}.Release|Any CPU.Build.0 = Release|Any CPU
		{A198F381-6AA9-423C-964A-3AFB375A5D10}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A198F381-6AA9-423C-964A-3AFB375A5D10}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A198F381-6AA9-423C-964A-3AFB375A5D10}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A198F381-6AA9-423C-964A-3AFB375A5D10}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AFC8B5E-0AF9-484A-BBF2-65BE386164EF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AFC8B5E-0AF9-484A-BBF2-65BE386164EF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AFC8B5E-0AF9-484A-BBF2-65BE386164EF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AFC8B5E-0AF9-484A-BBF2-65BE386164EF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3209505A-002C-479D-8312-BD60A93AF8F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3209505A-002C-479D-8312-BD60A93AF8F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3209505A-002C-479D-8312-BD60A93AF8F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3209505A-002C-479D-8312-BD60A93AF8F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{CEAAAD6F-1C90-4C94-AA70-3A127353D2B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEAAAD6F-1C90-4C94-AA70-3A127353D2B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEAAAD6F-1C90-4C94-AA70-3A127353D2B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEAAAD6F-1C90-4C94-AA70-3A127353D2B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC2243AD-C877-4532-A1F2-5533E605822F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC2243AD-C877-4532-A1F2-5533E605822F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC2243AD-C877-4532-A1F2-5533E605822F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC2243AD-C877-4532-A1F2-5533E605822F}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A4D2462-4E3E-4F41-B9DA-E270D07EA892}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A4D2462-4E3E-4F41-B9DA-E270D07EA892}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A4D2462-4E3E-4F41-B9DA-E270D07EA892}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A4D2462-4E3E-4F41-B9DA-E270D07EA892}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF042413-4827-4463-BEBE-D3C22075C111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF042413-4827-4463-BEBE-D3C22075C111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF042413-4827-4463-BEBE-D3C22075C111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF042413-4827-4463-BEBE-D3C22075C111}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BC7F111-0850-4272-A510-C49A5E650603}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BC7F111-0850-4272-A510-C49A5E650603}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BC7F111-0850-4272-A510-C49A5E650603}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BC7F111-0850-4272-A510-C49A5E650603}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7A43689-94E4-4EDF-B513-B9460AACE82F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7A43689-94E4-4EDF-B513-B9460AACE82F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7A43689-94E4-4EDF-B513-B9460AACE82F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7A43689-94E4-4EDF-B513-B9460AACE82F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F643824-1DF5-4541-9E51-1DFC80F2F193}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F643824-1DF5-4541-9E51-1DFC80F2F193}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F643824-1DF5-4541-9E51-1DFC80F2F193}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F643824-1DF5-4541-9E51-1DFC80F2F193}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB9BAFDD-D26B-4758-BE34-F726B844034F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB9BAFDD-D26B-4758-BE34-F726B844034F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB9BAFDD-D26B-4758-BE34-F726B844034F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB9BAFDD-D26B-4758-BE34-F726B844034F}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7C5CB4B-32C5-4D26-B15F-C8AC992D4151}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7C5CB4B-32C5-4D26-B15F-C8AC992D4151}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7C5CB4B-32C5-4D26-B15F-C8AC992D4151}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7C5CB4B-32C5-4D26-B15F-C8AC992D4151}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2EBC4D1-35FD-4A2D-9979-F3FAB180D44A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2EBC4D1-35FD-4A2D-9979-F3FAB180D44A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2EBC4D1-35FD-4A2D-9979-F3FAB180D44A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2EBC4D1-35FD-4A2D-9979-F3FAB180D44A}.Release|Any CPU.Build.0 = Release|Any CPU
		{0507CB74-E7B8-456C-95DB-279478E351E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0507CB74-E7B8-456C-95DB-279478E351E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0507CB74-E7B8-456C-95DB-279478E351E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0507CB74-E7B8-456C-95DB-279478E351E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C65325E-1CF0-40A0-9716-BB59A64542B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C65325E-1CF0-40A0-9716-BB59A64542B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C65325E-1CF0-40A0-9716-BB59A64542B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C65325E-1CF0-40A0-9716-BB59A64542B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{C8D3E38E-5DB8-4C6B-9831-9E30C179BF06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C8D3E38E-5DB8-4C6B-9831-9E30C179BF06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C8D3E38E-5DB8-4C6B-9831-9E30C179BF06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C8D3E38E-5DB8-4C6B-9831-9E30C179BF06}.Release|Any CPU.Build.0 = Release|Any CPU
		{5F0D186D-24EC-4687-A293-B000D132601D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F0D186D-24EC-4687-A293-B000D132601D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F0D186D-24EC-4687-A293-B000D132601D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F0D186D-24EC-4687-A293-B000D132601D}.Release|Any CPU.Build.0 = Release|Any CPU
		{8114D602-6CE4-4C8C-89E8-DB38C774ADCF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8114D602-6CE4-4C8C-89E8-DB38C774ADCF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8114D602-6CE4-4C8C-89E8-DB38C774ADCF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8114D602-6CE4-4C8C-89E8-DB38C774ADCF}.Release|Any CPU.Build.0 = Release|Any CPU
		{8CA2F6F6-50D0-4CED-89D0-18B1FF2AC959}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CA2F6F6-50D0-4CED-89D0-18B1FF2AC959}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CA2F6F6-50D0-4CED-89D0-18B1FF2AC959}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CA2F6F6-50D0-4CED-89D0-18B1FF2AC959}.Release|Any CPU.Build.0 = Release|Any CPU
		{909F646A-05CA-45D2-8C8E-1846026837F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{909F646A-05CA-45D2-8C8E-1846026837F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{909F646A-05CA-45D2-8C8E-1846026837F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{909F646A-05CA-45D2-8C8E-1846026837F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{978606CA-1C76-4C2C-978B-967655AD8453}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{978606CA-1C76-4C2C-978B-967655AD8453}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{978606CA-1C76-4C2C-978B-967655AD8453}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{978606CA-1C76-4C2C-978B-967655AD8453}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACE344AE-5366-77FF-AE8C-902DF75FBBEC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACE344AE-5366-77FF-AE8C-902DF75FBBEC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACE344AE-5366-77FF-AE8C-902DF75FBBEC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACE344AE-5366-77FF-AE8C-902DF75FBBEC}.Release|Any CPU.Build.0 = Release|Any CPU
		{51D1016C-3A56-69D9-34D5-E5674ECFFC58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51D1016C-3A56-69D9-34D5-E5674ECFFC58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51D1016C-3A56-69D9-34D5-E5674ECFFC58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{51D1016C-3A56-69D9-34D5-E5674ECFFC58}.Release|Any CPU.Build.0 = Release|Any CPU
		{144232ED-BB04-BC94-7E87-0B73343C51B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{144232ED-BB04-BC94-7E87-0B73343C51B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{144232ED-BB04-BC94-7E87-0B73343C51B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{144232ED-BB04-BC94-7E87-0B73343C51B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA746F00-4ADE-344D-7363-3AA4502781AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA746F00-4ADE-344D-7363-3AA4502781AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA746F00-4ADE-344D-7363-3AA4502781AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA746F00-4ADE-344D-7363-3AA4502781AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB908F45-6ABA-5962-B11E-3171BEDF6188}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB908F45-6ABA-5962-B11E-3171BEDF6188}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB908F45-6ABA-5962-B11E-3171BEDF6188}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB908F45-6ABA-5962-B11E-3171BEDF6188}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7CA5021-C7B4-BFD4-2E03-BAA53B8A0087}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7CA5021-C7B4-BFD4-2E03-BAA53B8A0087}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7CA5021-C7B4-BFD4-2E03-BAA53B8A0087}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7CA5021-C7B4-BFD4-2E03-BAA53B8A0087}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A5DE779-E32D-F53E-D465-6FCF26DA9633}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A5DE779-E32D-F53E-D465-6FCF26DA9633}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A5DE779-E32D-F53E-D465-6FCF26DA9633}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A5DE779-E32D-F53E-D465-6FCF26DA9633}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FCABDC6-2CE1-5D17-BD7D-E6DA915BB74F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FCABDC6-2CE1-5D17-BD7D-E6DA915BB74F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FCABDC6-2CE1-5D17-BD7D-E6DA915BB74F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FCABDC6-2CE1-5D17-BD7D-E6DA915BB74F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3DE48537-3C3D-2E2E-CC29-9B96446E2C79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3DE48537-3C3D-2E2E-CC29-9B96446E2C79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3DE48537-3C3D-2E2E-CC29-9B96446E2C79}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3DE48537-3C3D-2E2E-CC29-9B96446E2C79}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CCA0BBC-98C9-9A24-9F05-704F1DB415ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CCA0BBC-98C9-9A24-9F05-704F1DB415ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CCA0BBC-98C9-9A24-9F05-704F1DB415ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CCA0BBC-98C9-9A24-9F05-704F1DB415ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{9001C3FA-9E36-1FC2-CF5E-689B5E4484AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9001C3FA-9E36-1FC2-CF5E-689B5E4484AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9001C3FA-9E36-1FC2-CF5E-689B5E4484AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9001C3FA-9E36-1FC2-CF5E-689B5E4484AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{2F494372-3BE5-41B6-3042-EEE60B65BB2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2F494372-3BE5-41B6-3042-EEE60B65BB2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2F494372-3BE5-41B6-3042-EEE60B65BB2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2F494372-3BE5-41B6-3042-EEE60B65BB2F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FFDA7BF3-82E4-47E6-9C00-D529305595ED}
	EndGlobalSection
EndGlobal
