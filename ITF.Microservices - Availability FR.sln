Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{6955FF57-95B0-41E6-8087-B797ADD1009D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{AAF33B63-5DA4-4565-9162-A82D22947351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries.UnitTests", "src\ITF.SharedLibraries\tests\ITF.SharedLibraries.UnitTests\ITF.SharedLibraries.UnitTests.csproj", "{********-7CD4-491B-8CEB-806CC9A51D35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels.UnitTests", "src\ITF.SharedModels\tests\ITF.SharedModels.UnitTests\ITF.SharedModels.UnitTests.csproj", "{5DCD0871-E480-4B7E-A432-248A47D5C165}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.UnitTests", "src\ITF.Microservices.Availability\tests\ITF.Microservices.Availability.UnitTests\ITF.Microservices.Availability.UnitTests.csproj", "{0AE5F395-8665-45CE-846F-896F5476A375}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability", "src\ITF.Microservices.Availability\src\ITF.Microservices.Availability.csproj", "{F538B31D-22EB-4582-925B-A3A07D8AB639}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CatalogSynchronizer", "src\ITF.Microservices.CatalogSynchronizer\src\ITF.Microservices.CatalogSynchronizer.csproj", "{AA5C3A03-2753-4123-99D2-8E314F4A5F00}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.CatalogSynchronizer.UnitTests", "src\ITF.Microservices.CatalogSynchronizer\tests\ITF.Microservices.CatalogSynchronizer.UnitTests\ITF.Microservices.CatalogSynchronizer.UnitTests.csproj", "{3993E394-85F8-41D0-93E9-A3F18523D1F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSynchronizer", "src\ITF.Microservices.FloristSynchronizer\src\ITF.Microservices.FloristSynchronizer.csproj", "{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedAssignmentRules", "src\ITF.SharedAssignmentRules\src\ITF.SharedAssignmentRules.csproj", "{7E2E9FC3-46A8-4785-A042-E10C2AADE047}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedAssignmentRules.UnitTests", "src\ITF.SharedAssignmentRules\tests\ITF.SharedAssignmentRules.UnitTests\ITF.SharedAssignmentRules.UnitTests.csproj", "{5511F41E-DCE6-463F-9902-338193BBF8BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyOrderEvents", "src\ITF.Microservices.LegacyOrderEvents\src\ITF.Microservices.LegacyOrderEvents.csproj", "{E99F4281-007C-400D-B927-2D9494C8EB89}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyOrderEvents.UnitTests", "src\ITF.Microservices.LegacyOrderEvents\tests\ITF.Microservices.LegacyOrderEvents.UnitTests\ITF.Microservices.LegacyOrderEvents.UnitTests.csproj", "{347FA710-17D7-48C6-BCAD-B1096F36C538}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyFloristEvents", "src\ITF.Microservices.LegacyFloristEvents\src\ITF.Microservices.LegacyFloristEvents.csproj", "{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITF.Microservices.Availability.CatalogSubset.Synchronizer\src\ITF.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITF.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITF.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITF.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{90C52F44-D72C-44EC-9902-654BF8D38585}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.FloristSubset.Synchronizer", "src\ITF.Microservices.Availability.FloristSubset.Synchronizer\src\ITF.Microservices.Availability.FloristSubset.Synchronizer.csproj", "{6F026BC9-B612-4794-B784-8DD105CA684C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.OrderSubset.Synchronizer", "src\ITF.Microservices.Availability.OrderSubset.Synchronizer\src\ITF.Microservices.Availability.OrderSubset.Synchronizer.csproj", "{F4BAF067-F4EF-460C-9B99-7B93854173A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.StaticSubset.Provider", "src\ITF.Microservices.Availability.StaticSubset.Provider\src\ITF.Microservices.Availability.StaticSubset.Provider.csproj", "{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.GFS.Supplier", "src\IT.Microservices.GFS.Supplier\src\IT.Microservices.GFS.Supplier.csproj", "{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.GFS.Supplier.UnitTests", "src\IT.Microservices.GFS.Supplier\tests\IT.Microservices.GFS.Supplier.UnitTests\IT.Microservices.GFS.Supplier.UnitTests.csproj", "{369790E3-B571-4B65-BAC3-261788251B60}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common.UnitTests", "src\ITF.Lib.Common\tests\ITF.Lib.Common.UnitTests\ITF.Lib.Common.UnitTests.csproj", "{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.LegacyFloristEvents.UnitTests", "src\ITF.Microservices.LegacyFloristEvents\tests\ITF.Microservices.LegacyFloristEvents.UnitTests\ITF.Microservices.LegacyFloristEvents.UnitTests.csproj", "{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Availability.Library", "src\ITF.Availability.Library\src\ITF.Availability.Library.csproj", "{23472C4A-B806-4692-9F09-6CA7A62233A3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Availability.Library.UnitTests", "src\ITF.Availability.Library\tests\ITF.Availability.Library.UnitTests\ITF.Availability.Library.UnitTests.csproj", "{D56012A0-53C0-4A7D-BB18-358015DDFBB2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FloristSynchronizer.UnitTests", "src\ITF.Microservices.FloristSynchronizer\tests\ITF.Microservices.FloristSynchronizer.UnitTests\ITF.Microservices.FloristSynchronizer.UnitTests.csproj", "{DBF02C7A-2613-433B-B2A4-F60D839BCECB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.FloristSubset.Synchronizer.UnitTests", "src\ITF.Microservices.Availability.FloristSubset.Synchronizer\tests\ITF.Microservices.Availability.FloristSubset.Synchronizer.UnitTests\ITF.Microservices.Availability.FloristSubset.Synchronizer.UnitTests.csproj", "{B39E4C4C-6F5A-4F43-B6B2-C7D9D0662E87}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.OrderSubset.Synchronizer.UnitTests", "src\ITF.Microservices.Availability.OrderSubset.Synchronizer\tests\ITF.Microservices.Availability.OrderSubset.Synchronizer.UnitTests\ITF.Microservices.Availability.OrderSubset.Synchronizer.UnitTests.csproj", "{05F1B0D6-AB0A-40F0-9F8A-70753371BF70}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AssignementRules", "src\ITF.AssignementRules\src\ITF.AssignementRules.csproj", "{90A44E24-3D22-4061-A2A1-2959F3748D59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AssignementRules.UnitTests", "src\ITF.AssignementRules\tests\ITF.AssignementRules.UnitTests\ITF.AssignementRules.UnitTests.csproj", "{5EEAD594-348D-4E1C-A15B-2416AFC6AE4B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.Reactor", "src\ITF.Microservices.Availability.Reactor\src\ITF.Microservices.Availability.Reactor.csproj", "{7DEC8711-B4A4-4E3C-AB9D-78C11CEAE73F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Availability.Reactor.UnitTests", "src\ITF.Microservices.Availability.Reactor\tests\ITF.Microservices.Availability.Reactor.UnitTests\ITF.Microservices.Availability.Reactor.UnitTests.csproj", "{B6573D31-A847-4D4A-8F6A-82440FF88958}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules", "src\ITF.AvailabilityRules\src\ITF.AvailabilityRules.csproj", "{D2667B99-8678-4148-9A09-564EE0127495}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules.UnitTests", "src\ITF.AvailabilityRules\tests\ITF.AvailabilityRules.UnitTests\ITF.AvailabilityRules.UnitTests.csproj", "{512A3C0E-D16E-4DA7-BB08-F76574C4324D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FreshPortal.Synchronizer", "src\ITF.Microservices.FreshPortal.Synchronizer\src\ITF.Microservices.FreshPortal.Synchronizer.csproj", "{5F4ACC13-E41D-4348-8219-8010E71F06DF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.FreshPortal.Synchronizer.UnitTests", "src\ITF.Microservices.FreshPortal.Synchronizer\tests\ITF.Microservices.FreshPortal.Synchronizer.UnitTests\ITF.Microservices.FreshPortal.Synchronizer.UnitTests.csproj", "{A3C47562-ADF6-4CDD-9BC3-89CA9076BCF6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.EmailEmarsys.Reactor", "src\IT.Microservices.EmailEmarsys.Reactor\src\IT.Microservices.EmailEmarsys.Reactor.csproj", "{EF463A67-D228-48C8-A696-66082EC695FF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.Build.0 = Release|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0AE5F395-8665-45CE-846F-896F5476A375}.Release|Any CPU.Build.0 = Release|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F538B31D-22EB-4582-925B-A3A07D8AB639}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA5C3A03-2753-4123-99D2-8E314F4A5F00}.Release|Any CPU.Build.0 = Release|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3993E394-85F8-41D0-93E9-A3F18523D1F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CFB65F6-3B7E-4F12-9694-AC6A55B80106}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E2E9FC3-46A8-4785-A042-E10C2AADE047}.Release|Any CPU.Build.0 = Release|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5511F41E-DCE6-463F-9902-338193BBF8BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E99F4281-007C-400D-B927-2D9494C8EB89}.Release|Any CPU.Build.0 = Release|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{347FA710-17D7-48C6-BCAD-B1096F36C538}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3AEB6F0-AF84-410E-BE78-3A5349583B3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19317BDB-18D9-4C7B-8EAD-9E3E5EF2E4F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90C52F44-D72C-44EC-9902-654BF8D38585}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F026BC9-B612-4794-B784-8DD105CA684C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4BAF067-F4EF-460C-9B99-7B93854173A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D8756AE-0EAC-4D4A-8617-9E852E43EAD8}.Release|Any CPU.Build.0 = Release|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29008CEC-CEFE-4EEA-BDFD-B2B650675E2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{369790E3-B571-4B65-BAC3-261788251B60}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{52403A3F-F48C-43AA-8A2A-A73BC661A0A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23472C4A-B806-4692-9F09-6CA7A62233A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D56012A0-53C0-4A7D-BB18-358015DDFBB2}.Release|Any CPU.Build.0 = Release|Any CPU
		{DBF02C7A-2613-433B-B2A4-F60D839BCECB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DBF02C7A-2613-433B-B2A4-F60D839BCECB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DBF02C7A-2613-433B-B2A4-F60D839BCECB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DBF02C7A-2613-433B-B2A4-F60D839BCECB}.Release|Any CPU.Build.0 = Release|Any CPU
		{B39E4C4C-6F5A-4F43-B6B2-C7D9D0662E87}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B39E4C4C-6F5A-4F43-B6B2-C7D9D0662E87}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B39E4C4C-6F5A-4F43-B6B2-C7D9D0662E87}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B39E4C4C-6F5A-4F43-B6B2-C7D9D0662E87}.Release|Any CPU.Build.0 = Release|Any CPU
		{05F1B0D6-AB0A-40F0-9F8A-70753371BF70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05F1B0D6-AB0A-40F0-9F8A-70753371BF70}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05F1B0D6-AB0A-40F0-9F8A-70753371BF70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05F1B0D6-AB0A-40F0-9F8A-70753371BF70}.Release|Any CPU.Build.0 = Release|Any CPU
		{90A44E24-3D22-4061-A2A1-2959F3748D59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90A44E24-3D22-4061-A2A1-2959F3748D59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90A44E24-3D22-4061-A2A1-2959F3748D59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90A44E24-3D22-4061-A2A1-2959F3748D59}.Release|Any CPU.Build.0 = Release|Any CPU
		{5EEAD594-348D-4E1C-A15B-2416AFC6AE4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5EEAD594-348D-4E1C-A15B-2416AFC6AE4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5EEAD594-348D-4E1C-A15B-2416AFC6AE4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5EEAD594-348D-4E1C-A15B-2416AFC6AE4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DEC8711-B4A4-4E3C-AB9D-78C11CEAE73F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DEC8711-B4A4-4E3C-AB9D-78C11CEAE73F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DEC8711-B4A4-4E3C-AB9D-78C11CEAE73F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DEC8711-B4A4-4E3C-AB9D-78C11CEAE73F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6573D31-A847-4D4A-8F6A-82440FF88958}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6573D31-A847-4D4A-8F6A-82440FF88958}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6573D31-A847-4D4A-8F6A-82440FF88958}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6573D31-A847-4D4A-8F6A-82440FF88958}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2667B99-8678-4148-9A09-564EE0127495}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2667B99-8678-4148-9A09-564EE0127495}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2667B99-8678-4148-9A09-564EE0127495}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2667B99-8678-4148-9A09-564EE0127495}.Release|Any CPU.Build.0 = Release|Any CPU
		{512A3C0E-D16E-4DA7-BB08-F76574C4324D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{512A3C0E-D16E-4DA7-BB08-F76574C4324D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{512A3C0E-D16E-4DA7-BB08-F76574C4324D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{512A3C0E-D16E-4DA7-BB08-F76574C4324D}.Release|Any CPU.Build.0 = Release|Any CPU
		{5F4ACC13-E41D-4348-8219-8010E71F06DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F4ACC13-E41D-4348-8219-8010E71F06DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F4ACC13-E41D-4348-8219-8010E71F06DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F4ACC13-E41D-4348-8219-8010E71F06DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3C47562-ADF6-4CDD-9BC3-89CA9076BCF6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3C47562-ADF6-4CDD-9BC3-89CA9076BCF6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3C47562-ADF6-4CDD-9BC3-89CA9076BCF6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3C47562-ADF6-4CDD-9BC3-89CA9076BCF6}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF463A67-D228-48C8-A696-66082EC695FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF463A67-D228-48C8-A696-66082EC695FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF463A67-D228-48C8-A696-66082EC695FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF463A67-D228-48C8-A696-66082EC695FF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FFDA7BF3-82E4-47E6-9C00-D529305595ED}
	EndGlobalSection
EndGlobal
