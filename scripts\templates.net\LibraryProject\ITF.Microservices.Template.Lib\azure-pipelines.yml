# Build and push a Nuget package to a Private Feed 
# V.C 13/05/2024

trigger:
- master

resources:
- repo: self

variables:
  projectName: 'ITF.Microservices.Template.Lib'
  vstsFeed: '$(vstsRestoredFeed)'
  publishVstsFeed: '$(VstsPublishedFeed)'
  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
- template: build_deploy_lib.yml
  parameters:
    vmImageName: $(vmImageName)
    projectName: $(projectName)
    vstsFeed: $(vstsFeed)
    publishVstsFeed: $(publishVstsFeed)