#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
IT.SharedLibraries.CT
ITF.Lib.Common
ITF.SharedLibraries
ITF.SharedModels
ITE.Order.Library
ITF.Order.Library
ITI.Order.Library
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY pull
done

REPOSITORIESOCTO=(
IT.Microservices.EmailSender
IT.Microservices.Cron.PfsPurge
IT.Microservices.CT.Order.Synchronizer
IT.Microservices.CT.Order.Wrapper
IT.Microservices.Documents
IT.Microservices.Florist
IT.Microservices.FloristReactor
IT.Microservices.Florist.Queries
IT.Microservices.HttpToKafkaWrapper
IT.Microservices.KafkaToHttpReactor
IT.Microservices.OrderLogHistoryReactor
IT.Microservices.OrderReactor
IT.Microservices.SequenceGenerator
ITI.Microservices.OrderSubset.Synchronizer
IT.Microservices.NotificationSupplier
IT.Microservices.PFS.BackOffice
IT.Microservices.CourrierWrapper
IT.Microservices.FileUploader
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================  
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done

