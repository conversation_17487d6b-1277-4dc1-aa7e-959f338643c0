Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{6955FF57-95B0-41E6-8087-B797ADD1009D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{AAF33B63-5DA4-4565-9162-A82D22947351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries.UnitTests", "src\ITF.SharedLibraries\tests\ITF.SharedLibraries.UnitTests\ITF.SharedLibraries.UnitTests.csproj", "{********-7CD4-491B-8CEB-806CC9A51D35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels.UnitTests", "src\ITF.SharedModels\tests\ITF.SharedModels.UnitTests\ITF.SharedModels.UnitTests.csproj", "{5DCD0871-E480-4B7E-A432-248A47D5C165}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer", "src\IT.Microservices.CT.Product.Synchronizer\src\IT.Microservices.CT.Product.Synchronizer.csproj", "{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer", "src\IT.Microservices.CT.Order.Synchronizer\src\IT.Microservices.CT.Order.Synchronizer.csproj", "{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer.UnitTests", "src\IT.Microservices.CT.Product.Synchronizer\tests\IT.Microservices.CT.Product.Synchronizer.UnitTests\IT.Microservices.CT.Product.Synchronizer.UnitTests.csproj", "{9A3B54D1-EB60-4205-A888-720449332CA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability", "src\IT.Microservices.Availability\src\IT.Microservices.Availability.csproj", "{0B09E6D6-058B-4701-8CDB-EB9591437ED2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability.UnitTests", "src\IT.Microservices.Availability\tests\IT.Microservices.Availability.UnitTests\IT.Microservices.Availability.UnitTests.csproj", "{53450071-5EB2-4544-9371-D5237ED99A1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common.UnitTests", "src\ITF.Lib.Common\tests\ITF.Lib.Common.UnitTests\ITF.Lib.Common.UnitTests.csproj", "{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer.UnitTests", "src\IT.Microservices.CT.Order.Synchronizer\tests\IT.Microservices.CT.Order.Synchronizer.UnitTests\IT.Microservices.CT.Order.Synchronizer.UnitTests.csproj", "{6A124125-1251-48D0-91B7-4EF563085A6B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules", "src\ITP.AvailabilityRules\src\ITP.AvailabilityRules.csproj", "{1C13F104-611D-4044-A405-649E90224B26}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules", "src\ITE.AvailabilityRules\src\ITE.AvailabilityRules.csproj", "{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.ProductSubset.Synchronizer", "src\ITP.Microservices.ProductSubset.Synchronizer\src\ITP.Microservices.ProductSubset.Synchronizer.csproj", "{38C98F5F-BA4C-4ABC-8CF1-2B7654ED4BFB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.ProductSubset.Synchronizer", "src\ITE.Microservices.ProductSubset.Synchronizer\src\ITE.Microservices.ProductSubset.Synchronizer.csproj", "{45CF4649-82E6-44C9-8783-EBDAC840AA06}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITP.Microservices.ProductSubset.Synchronizer\tests\ITP.Microservices.ProductSubset.Synchronizer.UnitTests\ITP.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{6D5BD053-7996-4591-B25C-BCEA8A3DCFE5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.OrderSubset.Synchronizer", "src\ITP.Microservices.OrderSubset.Synchronizer\src\ITP.Microservices.OrderSubset.Synchronizer.csproj", "{D51F5B31-7383-46AA-A448-47C7074CAAD5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITP.Microservices.OrderSubset.Synchronizer\tests\ITP.Microservices.OrderSubset.Synchronizer.UnitTests\ITP.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{683AF83A-4766-457D-BF79-BB9A20C67AB7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.OrderSubset.Synchronizer", "src\ITE.Microservices.OrderSubset.Synchronizer\src\ITE.Microservices.OrderSubset.Synchronizer.csproj", "{6DE22634-9007-4B48-8FD1-ECAD24D8EBEA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITE.Microservices.OrderSubset.Synchronizer\tests\ITE.Microservices.OrderSubset.Synchronizer.UnitTests\ITE.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{7D7A77CE-9FB1-40FD-A443-F4B317D3BEA9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITE.Microservices.ProductSubset.Synchronizer\tests\ITE.Microservices.ProductSubset.Synchronizer.UnitTests\ITE.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{7EE8F6A8-BBC5-4656-8744-FF7F5E1AE871}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITP.Microservices.Availability.CatalogSubset.Synchronizer\src\ITP.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{38E38993-025B-4A01-AB4C-63FE0FDB9E0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITP.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITP.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITP.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{04465D16-7605-43CA-A5A9-377BC2F44AA2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITE.Microservices.Availability.CatalogSubset.Synchronizer\src\ITE.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{FFE1A778-4945-4AE9-A7BA-B06E8456EEEC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITE.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITE.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITE.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{BA16E859-136B-4904-A02B-986906A632AE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules.UnitTests", "src\ITP.AvailabilityRules\tests\ITP.AvailabilityRules.UnitTests\ITP.AvailabilityRules.UnitTests.csproj", "{F0B5EA65-231A-41FE-9E2F-7C9FCDBEDC00}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules.UnitTests", "src\ITE.AvailabilityRules\tests\ITE.AvailabilityRules.UnitTests\ITE.AvailabilityRules.UnitTests.csproj", "{B6E58EE1-BBB2-483C-A127-AF727796C434}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules", "src\ITF.AvailabilityRules\src\ITF.AvailabilityRules.csproj", "{A8CA220E-D642-4456-AE4F-0D4186C96D77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules.UnitTests", "src\ITF.AvailabilityRules\tests\ITF.AvailabilityRules.UnitTests\ITF.AvailabilityRules.UnitTests.csproj", "{8FDDB6D4-1EE4-4186-9335-532A138DC334}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules", "src\ITI2.AvailabilityRules\src\ITI2.AvailabilityRules.csproj", "{A7DCF67E-3F31-46DE-A02D-2C530A81164F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules.UnitTests", "src\ITI2.AvailabilityRules\tests\ITI2.AvailabilityRules.UnitTests\ITI2.AvailabilityRules.UnitTests.csproj", "{5CECC8DB-4D5C-4BE1-ADBE-CD28D37EE7ED}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.Availability.CatalogSubset.Synchronizer", "src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer\src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.csproj", "{61F238AB-C518-4628-8EA2-CE9896A6E1C2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.Availability.CatalogSubset.Synchronizer\tests\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests\ITI2.Microservices.Availability.CatalogSubset.Synchronizer.UnitTests.csproj", "{3A6A61BF-63A3-427F-B258-A3915B082B4B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.OrderSubset.Synchronizer", "src\ITI2.Microservices.OrderSubset.Synchronizer\src\ITI2.Microservices.OrderSubset.Synchronizer.csproj", "{20DD3AFB-D97C-4B76-B1BE-77ED85169761}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.OrderSubset.Synchronizer\tests\ITI2.Microservices.OrderSubset.Synchronizer.UnitTests\ITI2.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{F8563BA6-FDC0-4035-B860-F363A52D207F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.ProductSubset.Synchronizer", "src\ITI2.Microservices.ProductSubset.Synchronizer\src\ITI2.Microservices.ProductSubset.Synchronizer.csproj", "{52ED595F-D6E8-47EC-9762-13759050308C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.Microservices.ProductSubset.Synchronizer.UnitTests", "src\ITI2.Microservices.ProductSubset.Synchronizer\tests\ITI2.Microservices.ProductSubset.Synchronizer.UnitTests\ITI2.Microservices.ProductSubset.Synchronizer.UnitTests.csproj", "{7F3CA8A2-537F-4897-8A6C-76FBCCFEDAB3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.AvailabilityRules", "src\ITS.AvailabilityRules\src\ITS.AvailabilityRules.csproj", "{DCC96005-F827-4890-9F6B-0A9D8D71BE0C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT", "src\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj", "{91B9C820-720A-4C1C-BFA7-09D0BA7106AB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Release|Any CPU.Build.0 = Release|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{83048951-3959-446E-9BE0-CE7F3337F9E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83048951-3959-446E-9BE0-CE7F3337F9E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83048951-3959-446E-9BE0-CE7F3337F9E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83048951-3959-446E-9BE0-CE7F3337F9E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{38C98F5F-BA4C-4ABC-8CF1-2B7654ED4BFB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38C98F5F-BA4C-4ABC-8CF1-2B7654ED4BFB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38C98F5F-BA4C-4ABC-8CF1-2B7654ED4BFB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38C98F5F-BA4C-4ABC-8CF1-2B7654ED4BFB}.Release|Any CPU.Build.0 = Release|Any CPU
		{45CF4649-82E6-44C9-8783-EBDAC840AA06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45CF4649-82E6-44C9-8783-EBDAC840AA06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45CF4649-82E6-44C9-8783-EBDAC840AA06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45CF4649-82E6-44C9-8783-EBDAC840AA06}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D5BD053-7996-4591-B25C-BCEA8A3DCFE5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D5BD053-7996-4591-B25C-BCEA8A3DCFE5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D5BD053-7996-4591-B25C-BCEA8A3DCFE5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D5BD053-7996-4591-B25C-BCEA8A3DCFE5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D51F5B31-7383-46AA-A448-47C7074CAAD5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D51F5B31-7383-46AA-A448-47C7074CAAD5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D51F5B31-7383-46AA-A448-47C7074CAAD5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D51F5B31-7383-46AA-A448-47C7074CAAD5}.Release|Any CPU.Build.0 = Release|Any CPU
		{683AF83A-4766-457D-BF79-BB9A20C67AB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{683AF83A-4766-457D-BF79-BB9A20C67AB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{683AF83A-4766-457D-BF79-BB9A20C67AB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{683AF83A-4766-457D-BF79-BB9A20C67AB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DE22634-9007-4B48-8FD1-ECAD24D8EBEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DE22634-9007-4B48-8FD1-ECAD24D8EBEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DE22634-9007-4B48-8FD1-ECAD24D8EBEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DE22634-9007-4B48-8FD1-ECAD24D8EBEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D7A77CE-9FB1-40FD-A443-F4B317D3BEA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D7A77CE-9FB1-40FD-A443-F4B317D3BEA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D7A77CE-9FB1-40FD-A443-F4B317D3BEA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D7A77CE-9FB1-40FD-A443-F4B317D3BEA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{7EE8F6A8-BBC5-4656-8744-FF7F5E1AE871}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7EE8F6A8-BBC5-4656-8744-FF7F5E1AE871}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7EE8F6A8-BBC5-4656-8744-FF7F5E1AE871}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7EE8F6A8-BBC5-4656-8744-FF7F5E1AE871}.Release|Any CPU.Build.0 = Release|Any CPU
		{38E38993-025B-4A01-AB4C-63FE0FDB9E0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38E38993-025B-4A01-AB4C-63FE0FDB9E0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38E38993-025B-4A01-AB4C-63FE0FDB9E0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38E38993-025B-4A01-AB4C-63FE0FDB9E0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{04465D16-7605-43CA-A5A9-377BC2F44AA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04465D16-7605-43CA-A5A9-377BC2F44AA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04465D16-7605-43CA-A5A9-377BC2F44AA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04465D16-7605-43CA-A5A9-377BC2F44AA2}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFE1A778-4945-4AE9-A7BA-B06E8456EEEC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFE1A778-4945-4AE9-A7BA-B06E8456EEEC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFE1A778-4945-4AE9-A7BA-B06E8456EEEC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFE1A778-4945-4AE9-A7BA-B06E8456EEEC}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA16E859-136B-4904-A02B-986906A632AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA16E859-136B-4904-A02B-986906A632AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA16E859-136B-4904-A02B-986906A632AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA16E859-136B-4904-A02B-986906A632AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0B5EA65-231A-41FE-9E2F-7C9FCDBEDC00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0B5EA65-231A-41FE-9E2F-7C9FCDBEDC00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0B5EA65-231A-41FE-9E2F-7C9FCDBEDC00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0B5EA65-231A-41FE-9E2F-7C9FCDBEDC00}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6E58EE1-BBB2-483C-A127-AF727796C434}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6E58EE1-BBB2-483C-A127-AF727796C434}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6E58EE1-BBB2-483C-A127-AF727796C434}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6E58EE1-BBB2-483C-A127-AF727796C434}.Release|Any CPU.Build.0 = Release|Any CPU
		{1531E750-09ED-49D3-A363-C614375ACF59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1531E750-09ED-49D3-A363-C614375ACF59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1531E750-09ED-49D3-A363-C614375ACF59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1531E750-09ED-49D3-A363-C614375ACF59}.Release|Any CPU.Build.0 = Release|Any CPU
		{2659488E-4336-4DDF-8189-FF69F485058E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2659488E-4336-4DDF-8189-FF69F485058E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2659488E-4336-4DDF-8189-FF69F485058E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2659488E-4336-4DDF-8189-FF69F485058E}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8CA220E-D642-4456-AE4F-0D4186C96D77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8CA220E-D642-4456-AE4F-0D4186C96D77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8CA220E-D642-4456-AE4F-0D4186C96D77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8CA220E-D642-4456-AE4F-0D4186C96D77}.Release|Any CPU.Build.0 = Release|Any CPU
		{8FDDB6D4-1EE4-4186-9335-532A138DC334}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FDDB6D4-1EE4-4186-9335-532A138DC334}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FDDB6D4-1EE4-4186-9335-532A138DC334}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FDDB6D4-1EE4-4186-9335-532A138DC334}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7DCF67E-3F31-46DE-A02D-2C530A81164F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7DCF67E-3F31-46DE-A02D-2C530A81164F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7DCF67E-3F31-46DE-A02D-2C530A81164F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7DCF67E-3F31-46DE-A02D-2C530A81164F}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CECC8DB-4D5C-4BE1-ADBE-CD28D37EE7ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CECC8DB-4D5C-4BE1-ADBE-CD28D37EE7ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CECC8DB-4D5C-4BE1-ADBE-CD28D37EE7ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CECC8DB-4D5C-4BE1-ADBE-CD28D37EE7ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{61F238AB-C518-4628-8EA2-CE9896A6E1C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61F238AB-C518-4628-8EA2-CE9896A6E1C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61F238AB-C518-4628-8EA2-CE9896A6E1C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61F238AB-C518-4628-8EA2-CE9896A6E1C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A6A61BF-63A3-427F-B258-A3915B082B4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A6A61BF-63A3-427F-B258-A3915B082B4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A6A61BF-63A3-427F-B258-A3915B082B4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A6A61BF-63A3-427F-B258-A3915B082B4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{20DD3AFB-D97C-4B76-B1BE-77ED85169761}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20DD3AFB-D97C-4B76-B1BE-77ED85169761}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20DD3AFB-D97C-4B76-B1BE-77ED85169761}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20DD3AFB-D97C-4B76-B1BE-77ED85169761}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8563BA6-FDC0-4035-B860-F363A52D207F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8563BA6-FDC0-4035-B860-F363A52D207F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8563BA6-FDC0-4035-B860-F363A52D207F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8563BA6-FDC0-4035-B860-F363A52D207F}.Release|Any CPU.Build.0 = Release|Any CPU
		{52ED595F-D6E8-47EC-9762-13759050308C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{52ED595F-D6E8-47EC-9762-13759050308C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{52ED595F-D6E8-47EC-9762-13759050308C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{52ED595F-D6E8-47EC-9762-13759050308C}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F3CA8A2-537F-4897-8A6C-76FBCCFEDAB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F3CA8A2-537F-4897-8A6C-76FBCCFEDAB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F3CA8A2-537F-4897-8A6C-76FBCCFEDAB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F3CA8A2-537F-4897-8A6C-76FBCCFEDAB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCC96005-F827-4890-9F6B-0A9D8D71BE0C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCC96005-F827-4890-9F6B-0A9D8D71BE0C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCC96005-F827-4890-9F6B-0A9D8D71BE0C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCC96005-F827-4890-9F6B-0A9D8D71BE0C}.Release|Any CPU.Build.0 = Release|Any CPU
		{91B9C820-720A-4C1C-BFA7-09D0BA7106AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{91B9C820-720A-4C1C-BFA7-09D0BA7106AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{91B9C820-720A-4C1C-BFA7-09D0BA7106AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{91B9C820-720A-4C1C-BFA7-09D0BA7106AB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FFDA7BF3-82E4-47E6-9C00-D529305595ED}
	EndGlobalSection
EndGlobal
