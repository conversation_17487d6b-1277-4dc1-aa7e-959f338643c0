{"format": 1, "restore": {"C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\tests\\ITF.Microservices.Template.Reactor.UnitTests\\ITF.Microservices.Template.Reactor.UnitTests.csproj": {}}, "projects": {"C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj": {"restore": {"projectUniqueName": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj", "projectPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}}}, "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\tests\\ITF.Microservices.Template.Reactor.UnitTests\\ITF.Microservices.Template.Reactor.UnitTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\tests\\ITF.Microservices.Template.Reactor.UnitTests\\ITF.Microservices.Template.Reactor.UnitTests.csproj", "projectName": "ITF.Microservices.Template.Reactor.UnitTests", "projectPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\tests\\ITF.Microservices.Template.Reactor.UnitTests\\ITF.Microservices.Template.Reactor.UnitTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\tests\\ITF.Microservices.Template.Reactor.UnitTests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj": {"projectPath": "C:\\MS\\scripts\\templates.net\\ReactorProject\\ITF.Microservices.Template.Reactor\\src\\ITF.Microservices.Template.Reactor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.10.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.5.0, )"}, "Moq": {"target": "Package", "version": "[4.18.4, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.2.0, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300/PortableRuntimeIdentifierGraph.json"}}}}}