﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{6955FF57-95B0-41E6-8087-B797ADD1009D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{AAF33B63-5DA4-4565-9162-A82D22947351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries.UnitTests", "src\ITF.SharedLibraries\tests\ITF.SharedLibraries.UnitTests\ITF.SharedLibraries.UnitTests.csproj", "{********-7CD4-491B-8CEB-806CC9A51D35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{D3C264F5-2DD3-4D32-856D-BD0FFCB7A4A5}"
	ProjectSection(SolutionItems) = preProject
		src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj = src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT", "src\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj", "{2C71459C-3005-4BBF-B423-16FD39182E51}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.KafkaToHttpReactor", "src\IT.Microservices.KafkaToHttpReactor\src\IT.Microservices.KafkaToHttpReactor.csproj", "{749AD061-A4FE-43F8-80C5-42A551D5B091}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.KafkaToHttpReactor.UnitTests", "src\IT.Microservices.KafkaToHttpReactor\tests\IT.Microservices.KafkaToHttpReactor.UnitTests\IT.Microservices.KafkaToHttpReactor.UnitTests.csproj", "{47EFA1E3-B9B2-4F1D-B650-D5EFAD254B7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Shipment", "src\IT.Microservices.Shipment\src\IT.Microservices.Shipment.csproj", "{9BA2E8F6-C00A-48E5-9E0B-B414FF1C9BD6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Shipment.UnitTests", "src\IT.Microservices.Shipment\tests\IT.Microservices.Shipment.UnitTests\IT.Microservices.Shipment.UnitTests.csproj", "{C5622349-A424-4691-B4C1-1C8EA3E7654E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{E14EE8E1-6361-447A-A415-463AFDAFA2B5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels.UnitTests", "src\ITF.SharedModels\tests\ITF.SharedModels.UnitTests\ITF.SharedModels.UnitTests.csproj", "{29BDB3C2-1D74-44B6-AB1D-75282A8BF026}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SE.CourierLib.Qapla", "src\SE.CourierLib.Qapla\src\SE.CourierLib.Qapla.csproj", "{BD0150EF-1250-4E87-B0BF-81A5D5E262BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SE.CourierLib.Qapla.UnitTests", "src\SE.CourierLib.Qapla\tests\SE.CourierLib.Qapla.UnitTests\SE.CourierLib.Qapla.UnitTests.csproj", "{0A72D230-556E-4B0D-9239-015A0DEBCFC4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.CourierLib.Shared", "src\IT.CourierLib.Shared\src\IT.CourierLib.Shared.csproj", "{94433A9E-8EF8-9548-C28C-32F1CDABDA86}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.CourierLib.Shared.UnitTests", "src\IT.CourierLib.Shared\tests\IT.CourierLib.Shared.UnitTests\IT.CourierLib.Shared.UnitTests.csproj", "{14B4E1A6-36C1-BAE4-DB10-32148C2CA8FF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CourrierWrapper", "src\IT.Microservices.CourrierWrapper\src\IT.Microservices.CourrierWrapper.csproj", "{81C2F63D-5F16-4008-86C2-CBCBE7C2CB17}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CourrierWrapper.UnitTests", "src\IT.Microservices.CourrierWrapper\tests\IT.Microservices.CourrierWrapper.UnitTests\IT.Microservices.CourrierWrapper.UnitTests.csproj", "{6089ABFF-E1F4-4640-98FC-222282905FC1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderReactor", "src\IT.Microservices.OrderReactor\src\IT.Microservices.OrderReactor.csproj", "{60E94650-3C00-CBDD-8EE0-F4E4DAF236E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.OrderReactor.UnitTests", "src\IT.Microservices.OrderReactor\tests\IT.Microservices.OrderReactor.UnitTests\IT.Microservices.OrderReactor.UnitTests.csproj", "{97FCD378-2A66-1264-3F7F-7DA143F00B33}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SE.CourierLib.GLS", "src\SE.CourierLib.GLS\src\SE.CourierLib.GLS.csproj", "{F01BB10E-FEE5-4975-B152-508DA038BDDF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SE.CourierLib.GLS.UnitTests", "src\SE.CourierLib.GLS\tests\SE.CourierLib.GLS.UnitTests\SE.CourierLib.GLS.UnitTests.csproj", "{F2EA9C49-69A1-48CF-8176-19860BD2BB01}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Order.Library", "src\ITF.Order.Library\src\ITF.Order.Library.csproj", "{8B63EA4E-46D8-499E-91AC-0D676F46134A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Order.Library.UnitTests", "src\ITF.Order.Library\tests\ITF.Order.Library.UnitTests\ITF.Order.Library.UnitTests.csproj", "{44A99132-6A7A-4842-A0D6-EC7168638191}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Order.Library", "src\ITE.Order.Library\src\ITE.Order.Library.csproj", "{E091C312-FF6A-497C-B71E-1E575539B5EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.Order.Library.UnitTests", "src\ITE.Order.Library\tests\ITE.Order.Library.UnitTests\ITE.Order.Library.UnitTests.csproj", "{E7687A73-035D-443E-900E-6C7A536C8B37}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Wrapper", "src\IT.Microservices.CT.Order.Wrapper\src\IT.Microservices.CT.Order.Wrapper.csproj", "{593D767C-9E0F-41CF-87DE-119B6CEAB1C4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Wrapper.UnitTests", "src\IT.Microservices.CT.Order.Wrapper\tests\IT.Microservices.CT.Order.Wrapper.UnitTests\IT.Microservices.CT.Order.Wrapper.UnitTests.csproj", "{47A433E4-CE23-4EAD-BE1F-8E18F75E07DD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SE.CourierLib.Paack", "src\SE.CourierLib.Paack\src\SE.CourierLib.Paack.csproj", "{DA5B6665-91BF-8BD5-0923-8AF2B0D9B796}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SE.CourierLib.Paack.UnitTests", "src\SE.CourierLib.Paack\tests\SE.CourierLib.Paack.UnitTests\SE.CourierLib.Paack.UnitTests.csproj", "{734913F0-A68E-3EC9-1209-CD0627C65F2A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C71459C-3005-4BBF-B423-16FD39182E51}.Release|Any CPU.Build.0 = Release|Any CPU
		{749AD061-A4FE-43F8-80C5-42A551D5B091}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{749AD061-A4FE-43F8-80C5-42A551D5B091}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{749AD061-A4FE-43F8-80C5-42A551D5B091}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{749AD061-A4FE-43F8-80C5-42A551D5B091}.Release|Any CPU.Build.0 = Release|Any CPU
		{47EFA1E3-B9B2-4F1D-B650-D5EFAD254B7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47EFA1E3-B9B2-4F1D-B650-D5EFAD254B7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47EFA1E3-B9B2-4F1D-B650-D5EFAD254B7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47EFA1E3-B9B2-4F1D-B650-D5EFAD254B7D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9BA2E8F6-C00A-48E5-9E0B-B414FF1C9BD6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9BA2E8F6-C00A-48E5-9E0B-B414FF1C9BD6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9BA2E8F6-C00A-48E5-9E0B-B414FF1C9BD6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9BA2E8F6-C00A-48E5-9E0B-B414FF1C9BD6}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5622349-A424-4691-B4C1-1C8EA3E7654E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5622349-A424-4691-B4C1-1C8EA3E7654E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5622349-A424-4691-B4C1-1C8EA3E7654E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5622349-A424-4691-B4C1-1C8EA3E7654E}.Release|Any CPU.Build.0 = Release|Any CPU
		{E14EE8E1-6361-447A-A415-463AFDAFA2B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E14EE8E1-6361-447A-A415-463AFDAFA2B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E14EE8E1-6361-447A-A415-463AFDAFA2B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E14EE8E1-6361-447A-A415-463AFDAFA2B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{29BDB3C2-1D74-44B6-AB1D-75282A8BF026}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29BDB3C2-1D74-44B6-AB1D-75282A8BF026}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29BDB3C2-1D74-44B6-AB1D-75282A8BF026}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29BDB3C2-1D74-44B6-AB1D-75282A8BF026}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD0150EF-1250-4E87-B0BF-81A5D5E262BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD0150EF-1250-4E87-B0BF-81A5D5E262BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD0150EF-1250-4E87-B0BF-81A5D5E262BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD0150EF-1250-4E87-B0BF-81A5D5E262BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{0A72D230-556E-4B0D-9239-015A0DEBCFC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0A72D230-556E-4B0D-9239-015A0DEBCFC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0A72D230-556E-4B0D-9239-015A0DEBCFC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0A72D230-556E-4B0D-9239-015A0DEBCFC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{94433A9E-8EF8-9548-C28C-32F1CDABDA86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94433A9E-8EF8-9548-C28C-32F1CDABDA86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94433A9E-8EF8-9548-C28C-32F1CDABDA86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94433A9E-8EF8-9548-C28C-32F1CDABDA86}.Release|Any CPU.Build.0 = Release|Any CPU
		{14B4E1A6-36C1-BAE4-DB10-32148C2CA8FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14B4E1A6-36C1-BAE4-DB10-32148C2CA8FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14B4E1A6-36C1-BAE4-DB10-32148C2CA8FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14B4E1A6-36C1-BAE4-DB10-32148C2CA8FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{81C2F63D-5F16-4008-86C2-CBCBE7C2CB17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81C2F63D-5F16-4008-86C2-CBCBE7C2CB17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81C2F63D-5F16-4008-86C2-CBCBE7C2CB17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81C2F63D-5F16-4008-86C2-CBCBE7C2CB17}.Release|Any CPU.Build.0 = Release|Any CPU
		{6089ABFF-E1F4-4640-98FC-222282905FC1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6089ABFF-E1F4-4640-98FC-222282905FC1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6089ABFF-E1F4-4640-98FC-222282905FC1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6089ABFF-E1F4-4640-98FC-222282905FC1}.Release|Any CPU.Build.0 = Release|Any CPU
		{60E94650-3C00-CBDD-8EE0-F4E4DAF236E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60E94650-3C00-CBDD-8EE0-F4E4DAF236E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60E94650-3C00-CBDD-8EE0-F4E4DAF236E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60E94650-3C00-CBDD-8EE0-F4E4DAF236E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{97FCD378-2A66-1264-3F7F-7DA143F00B33}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97FCD378-2A66-1264-3F7F-7DA143F00B33}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97FCD378-2A66-1264-3F7F-7DA143F00B33}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97FCD378-2A66-1264-3F7F-7DA143F00B33}.Release|Any CPU.Build.0 = Release|Any CPU
		{F01BB10E-FEE5-4975-B152-508DA038BDDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F01BB10E-FEE5-4975-B152-508DA038BDDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F01BB10E-FEE5-4975-B152-508DA038BDDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F01BB10E-FEE5-4975-B152-508DA038BDDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2EA9C49-69A1-48CF-8176-19860BD2BB01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2EA9C49-69A1-48CF-8176-19860BD2BB01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2EA9C49-69A1-48CF-8176-19860BD2BB01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2EA9C49-69A1-48CF-8176-19860BD2BB01}.Release|Any CPU.Build.0 = Release|Any CPU
		{D63260DD-03C7-4CE0-B278-93CE6BD78DC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D63260DD-03C7-4CE0-B278-93CE6BD78DC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D63260DD-03C7-4CE0-B278-93CE6BD78DC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D63260DD-03C7-4CE0-B278-93CE6BD78DC5}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4FC637A-AB18-4779-918C-2D0CDEB7C7C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4FC637A-AB18-4779-918C-2D0CDEB7C7C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4FC637A-AB18-4779-918C-2D0CDEB7C7C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4FC637A-AB18-4779-918C-2D0CDEB7C7C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B63EA4E-46D8-499E-91AC-0D676F46134A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B63EA4E-46D8-499E-91AC-0D676F46134A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B63EA4E-46D8-499E-91AC-0D676F46134A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B63EA4E-46D8-499E-91AC-0D676F46134A}.Release|Any CPU.Build.0 = Release|Any CPU
		{44A99132-6A7A-4842-A0D6-EC7168638191}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44A99132-6A7A-4842-A0D6-EC7168638191}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44A99132-6A7A-4842-A0D6-EC7168638191}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44A99132-6A7A-4842-A0D6-EC7168638191}.Release|Any CPU.Build.0 = Release|Any CPU
		{E091C312-FF6A-497C-B71E-1E575539B5EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E091C312-FF6A-497C-B71E-1E575539B5EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E091C312-FF6A-497C-B71E-1E575539B5EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E091C312-FF6A-497C-B71E-1E575539B5EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7687A73-035D-443E-900E-6C7A536C8B37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7687A73-035D-443E-900E-6C7A536C8B37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7687A73-035D-443E-900E-6C7A536C8B37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7687A73-035D-443E-900E-6C7A536C8B37}.Release|Any CPU.Build.0 = Release|Any CPU
		{593D767C-9E0F-41CF-87DE-119B6CEAB1C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{593D767C-9E0F-41CF-87DE-119B6CEAB1C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{593D767C-9E0F-41CF-87DE-119B6CEAB1C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{593D767C-9E0F-41CF-87DE-119B6CEAB1C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{47A433E4-CE23-4EAD-BE1F-8E18F75E07DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47A433E4-CE23-4EAD-BE1F-8E18F75E07DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47A433E4-CE23-4EAD-BE1F-8E18F75E07DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47A433E4-CE23-4EAD-BE1F-8E18F75E07DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA5B6665-91BF-8BD5-0923-8AF2B0D9B796}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA5B6665-91BF-8BD5-0923-8AF2B0D9B796}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA5B6665-91BF-8BD5-0923-8AF2B0D9B796}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA5B6665-91BF-8BD5-0923-8AF2B0D9B796}.Release|Any CPU.Build.0 = Release|Any CPU
		{734913F0-A68E-3EC9-1209-CD0627C65F2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{734913F0-A68E-3EC9-1209-CD0627C65F2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{734913F0-A68E-3EC9-1209-CD0627C65F2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{734913F0-A68E-3EC9-1209-CD0627C65F2A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FFDA7BF3-82E4-47E6-9C00-D529305595ED}
	EndGlobalSection
EndGlobal
