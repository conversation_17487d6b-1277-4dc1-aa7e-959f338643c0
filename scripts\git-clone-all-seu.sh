#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
ITF.Lib.Common
ITF.SharedLibraries
ITF.SharedModels
IT.SharedLibraries.CT
SE.CourierLib.GLS
SE.CourierLib.Qapla
SE.CourierLib.Paack
IT.CourierLib.Shared
ITE.Order.Library
ITF.Order.Library
ITI.Order.Library
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY $REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY pull
done
  
REPOSITORIESOCTO=(
IT.Microservices.CT.Order.Wrapper
IT.Microservices.CourrierWrapper
IT.Microservices.KafkaToHttpReactor
IT.Microservices.OrderReactor
IT.Microservices.Shipment
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY $REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done
