Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{6955FF57-95B0-41E6-8087-B797ADD1009D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{AAF33B63-5DA4-4565-9162-A82D22947351}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries.UnitTests", "src\ITF.SharedLibraries\tests\ITF.SharedLibraries.UnitTests\ITF.SharedLibraries.UnitTests.csproj", "{********-7CD4-491B-8CEB-806CC9A51D35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels.UnitTests", "src\ITF.SharedModels\tests\ITF.SharedModels.UnitTests\ITF.SharedModels.UnitTests.csproj", "{5DCD0871-E480-4B7E-A432-248A47D5C165}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer", "src\IT.Microservices.CT.Product.Synchronizer\src\IT.Microservices.CT.Product.Synchronizer.csproj", "{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer", "src\IT.Microservices.CT.Order.Synchronizer\src\IT.Microservices.CT.Order.Synchronizer.csproj", "{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Product.Synchronizer.UnitTests", "src\IT.Microservices.CT.Product.Synchronizer\tests\IT.Microservices.CT.Product.Synchronizer.UnitTests\IT.Microservices.CT.Product.Synchronizer.UnitTests.csproj", "{9A3B54D1-EB60-4205-A888-720449332CA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability", "src\IT.Microservices.Availability\src\IT.Microservices.Availability.csproj", "{0B09E6D6-058B-4701-8CDB-EB9591437ED2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.Availability.UnitTests", "src\IT.Microservices.Availability\tests\IT.Microservices.Availability.UnitTests\IT.Microservices.Availability.UnitTests.csproj", "{53450071-5EB2-4544-9371-D5237ED99A1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common.UnitTests", "src\ITF.Lib.Common\tests\ITF.Lib.Common.UnitTests\ITF.Lib.Common.UnitTests.csproj", "{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.Microservices.CT.Order.Synchronizer.UnitTests", "src\IT.Microservices.CT.Order.Synchronizer\tests\IT.Microservices.CT.Order.Synchronizer.UnitTests\IT.Microservices.CT.Order.Synchronizer.UnitTests.csproj", "{6A124125-1251-48D0-91B7-4EF563085A6B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules", "src\ITP.AvailabilityRules\src\ITP.AvailabilityRules.csproj", "{1C13F104-611D-4044-A405-649E90224B26}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules", "src\ITE.AvailabilityRules\src\ITE.AvailabilityRules.csproj", "{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITE.AvailabilityRules.UnitTests", "src\ITE.AvailabilityRules\tests\ITE.AvailabilityRules.UnitTests\ITE.AvailabilityRules.UnitTests.csproj", "{943AB993-C57A-43FD-8538-28D6E37A9CD9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITP.AvailabilityRules.UnitTests", "src\ITP.AvailabilityRules\tests\ITP.AvailabilityRules.UnitTests\ITP.AvailabilityRules.UnitTests.csproj", "{BD3169DA-E723-4F0A-8A20-BDD45251F732}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules.UnitTests", "src\ITF.AvailabilityRules\tests\ITF.AvailabilityRules.UnitTests\ITF.AvailabilityRules.UnitTests.csproj", "{EE7E74E7-8E9C-4F33-9B4A-8C314E9F4176}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.AvailabilityRules", "src\ITF.AvailabilityRules\src\ITF.AvailabilityRules.csproj", "{1B915DB5-459D-4FF2-814F-DE27C265D7E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules", "src\ITI2.AvailabilityRules\src\ITI2.AvailabilityRules.csproj", "{F0AA69F5-3446-48D3-B47D-95DF23F12275}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITI2.AvailabilityRules.UnitTests", "src\ITI2.AvailabilityRules\tests\ITI2.AvailabilityRules.UnitTests\ITI2.AvailabilityRules.UnitTests.csproj", "{D6CF7493-DCAB-4696-A2AA-82B618552C40}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT", "src\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj", "{BC0FFEE7-CBA4-407B-BA2B-305F4BCA9569}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IT.SharedLibraries.CT.UnitTests", "src\IT.SharedLibraries.CT\tests\IT.SharedLibraries.CT.UnitTests\IT.SharedLibraries.CT.UnitTests.csproj", "{FA184C8D-8F95-4682-9555-F26A77715765}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.AvailabilityRules", "src\ITS.AvailabilityRules\src\ITS.AvailabilityRules.csproj", "{D5CCA9D7-EB21-4D3E-88D0-98C4E2A2B08E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.AvailabilityRules.UnitTests", "src\ITS.AvailabilityRules\tests\ITS.AvailabilityRules.UnitTests\ITS.AvailabilityRules.UnitTests.csproj", "{885A8791-439D-414A-8E34-9543AA7A6973}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.Microservices.OrderSubset.Synchronizer", "src\ITS.Microservices.OrderSubset.Synchronizer\src\ITS.Microservices.OrderSubset.Synchronizer.csproj", "{AAE375EE-8D5D-49B7-B81D-B3F8DDD5D81C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.Microservices.OrderSubset.Synchronizer.UnitTests", "src\ITS.Microservices.OrderSubset.Synchronizer\tests\ITS.Microservices.OrderSubset.Synchronizer.UnitTests\ITS.Microservices.OrderSubset.Synchronizer.UnitTests.csproj", "{D0EFF951-74B5-48F1-8B23-C7A4B10E976C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.Microservices.Azure.ProductReactor", "src\ITS.Microservices.Azure.ProductReactor\src\ITS.Microservices.Azure.ProductReactor.csproj", "{3D49537F-3D2C-47AC-94CD-AB1611BDF0A0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITS.Microservices.Azure.ProductReactor.UnitTests", "src\ITS.Microservices.Azure.ProductReactor\tests\ITS.Microservices.Azure.ProductReactor.UnitTests\ITS.Microservices.Azure.ProductReactor.UnitTests.csproj", "{5760BCDA-1AF0-4B11-8806-BB2B87190025}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6955FF57-95B0-41E6-8087-B797ADD1009D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAF33B63-5DA4-4565-9162-A82D22947351}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-7CD4-491B-8CEB-806CC9A51D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5DCD0871-E480-4B7E-A432-248A47D5C165}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE381907-B2CA-42C2-8FDC-EBDF8A40586D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB90B34-4E34-4299-9C5E-B9C1B2ECAF4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB40E324-E88E-4AE9-84F0-D12A0C0C46B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA009B76-D8AB-4A89-8FDF-DD78EE0D9A09}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A3B54D1-EB60-4205-A888-720449332CA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B09E6D6-058B-4701-8CDB-EB9591437ED2}.Release|Any CPU.Build.0 = Release|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53450071-5EB2-4544-9371-D5237ED99A1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3669D98-B21D-4AD0-BBE9-0C558581AEF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A124125-1251-48D0-91B7-4EF563085A6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C13F104-611D-4044-A405-649E90224B26}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED9FAC40-B8FA-430F-9539-B574D54F9B1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{92951935-178D-42C0-BDC8-6FC6739FA322}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92951935-178D-42C0-BDC8-6FC6739FA322}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92951935-178D-42C0-BDC8-6FC6739FA322}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92951935-178D-42C0-BDC8-6FC6739FA322}.Release|Any CPU.Build.0 = Release|Any CPU
		{943AB993-C57A-43FD-8538-28D6E37A9CD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{943AB993-C57A-43FD-8538-28D6E37A9CD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{943AB993-C57A-43FD-8538-28D6E37A9CD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{943AB993-C57A-43FD-8538-28D6E37A9CD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD3169DA-E723-4F0A-8A20-BDD45251F732}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD3169DA-E723-4F0A-8A20-BDD45251F732}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD3169DA-E723-4F0A-8A20-BDD45251F732}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD3169DA-E723-4F0A-8A20-BDD45251F732}.Release|Any CPU.Build.0 = Release|Any CPU
		{941A5F1E-64B2-4270-9DCD-09619551F5EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{941A5F1E-64B2-4270-9DCD-09619551F5EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{941A5F1E-64B2-4270-9DCD-09619551F5EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{941A5F1E-64B2-4270-9DCD-09619551F5EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7C51E8CA-7DD0-4520-9B6C-CFB96C863EEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C51E8CA-7DD0-4520-9B6C-CFB96C863EEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C51E8CA-7DD0-4520-9B6C-CFB96C863EEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C51E8CA-7DD0-4520-9B6C-CFB96C863EEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE7E74E7-8E9C-4F33-9B4A-8C314E9F4176}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE7E74E7-8E9C-4F33-9B4A-8C314E9F4176}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE7E74E7-8E9C-4F33-9B4A-8C314E9F4176}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE7E74E7-8E9C-4F33-9B4A-8C314E9F4176}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B915DB5-459D-4FF2-814F-DE27C265D7E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B915DB5-459D-4FF2-814F-DE27C265D7E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B915DB5-459D-4FF2-814F-DE27C265D7E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B915DB5-459D-4FF2-814F-DE27C265D7E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0AA69F5-3446-48D3-B47D-95DF23F12275}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0AA69F5-3446-48D3-B47D-95DF23F12275}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0AA69F5-3446-48D3-B47D-95DF23F12275}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0AA69F5-3446-48D3-B47D-95DF23F12275}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6CF7493-DCAB-4696-A2AA-82B618552C40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6CF7493-DCAB-4696-A2AA-82B618552C40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6CF7493-DCAB-4696-A2AA-82B618552C40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6CF7493-DCAB-4696-A2AA-82B618552C40}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC0FFEE7-CBA4-407B-BA2B-305F4BCA9569}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC0FFEE7-CBA4-407B-BA2B-305F4BCA9569}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC0FFEE7-CBA4-407B-BA2B-305F4BCA9569}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC0FFEE7-CBA4-407B-BA2B-305F4BCA9569}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA184C8D-8F95-4682-9555-F26A77715765}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA184C8D-8F95-4682-9555-F26A77715765}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA184C8D-8F95-4682-9555-F26A77715765}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA184C8D-8F95-4682-9555-F26A77715765}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5CCA9D7-EB21-4D3E-88D0-98C4E2A2B08E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5CCA9D7-EB21-4D3E-88D0-98C4E2A2B08E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5CCA9D7-EB21-4D3E-88D0-98C4E2A2B08E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5CCA9D7-EB21-4D3E-88D0-98C4E2A2B08E}.Release|Any CPU.Build.0 = Release|Any CPU
		{885A8791-439D-414A-8E34-9543AA7A6973}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{885A8791-439D-414A-8E34-9543AA7A6973}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{885A8791-439D-414A-8E34-9543AA7A6973}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{885A8791-439D-414A-8E34-9543AA7A6973}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAE375EE-8D5D-49B7-B81D-B3F8DDD5D81C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAE375EE-8D5D-49B7-B81D-B3F8DDD5D81C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAE375EE-8D5D-49B7-B81D-B3F8DDD5D81C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAE375EE-8D5D-49B7-B81D-B3F8DDD5D81C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0EFF951-74B5-48F1-8B23-C7A4B10E976C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0EFF951-74B5-48F1-8B23-C7A4B10E976C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0EFF951-74B5-48F1-8B23-C7A4B10E976C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0EFF951-74B5-48F1-8B23-C7A4B10E976C}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D49537F-3D2C-47AC-94CD-AB1611BDF0A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D49537F-3D2C-47AC-94CD-AB1611BDF0A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D49537F-3D2C-47AC-94CD-AB1611BDF0A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D49537F-3D2C-47AC-94CD-AB1611BDF0A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{5760BCDA-1AF0-4B11-8806-BB2B87190025}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5760BCDA-1AF0-4B11-8806-BB2B87190025}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5760BCDA-1AF0-4B11-8806-BB2B87190025}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5760BCDA-1AF0-4B11-8806-BB2B87190025}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FFDA7BF3-82E4-47E6-9C00-D529305595ED}
	EndGlobalSection
EndGlobal
