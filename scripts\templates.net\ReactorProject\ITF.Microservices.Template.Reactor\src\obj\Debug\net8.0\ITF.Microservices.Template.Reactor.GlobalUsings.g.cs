// <auto-generated/>
global using global::CSharpFunctionalExtensions;
global using global::Elastic.Apm;
global using global::Elastic.Apm.Api;
global using global::ITF.Lib.Common.Error;
global using global::ITF.Microservices.Template.Reactor.Common;
global using global::ITF.SharedLibraries.ApplicationMetrics.Extensions;
global using global::ITF.SharedLibraries.EnvironmentVariable;
global using global::ITF.SharedLibraries.ExtensionMethods;
global using global::ITF.SharedLibraries.FeatureFlags;
global using global::ITF.SharedLibraries.Framework;
global using global::ITF.SharedLibraries.HealthCheck.Extensions;
global using global::ITF.SharedLibraries.HostBuilder.Extensions;
global using global::ITF.SharedLibraries.HttpClient;
global using global::ITF.SharedLibraries.Json;
global using global::ITF.SharedLibraries.Kafka;
global using global::ITF.SharedLibraries.Kafka.Extensions;
global using global::ITF.SharedLibraries.Kafka.Subscriber;
global using global::ITF.SharedLibraries.Polly;
global using global::ITF.SharedLibraries.Readyness.Extensions;
global using global::ITF.SharedLibraries.Swagger;
global using global::Microsoft.AspNetCore.Builder;
global using global::Microsoft.AspNetCore.Hosting;
global using global::Microsoft.AspNetCore.Http;
global using global::Microsoft.AspNetCore.Mvc;
global using global::Microsoft.AspNetCore.Mvc.ApiExplorer;
global using global::Microsoft.AspNetCore.Routing;
global using global::Microsoft.Extensions.Configuration;
global using global::Microsoft.Extensions.DependencyInjection;
global using global::Microsoft.Extensions.Hosting;
global using global::Microsoft.Extensions.Logging;
global using global::Microsoft.Extensions.Options;
global using global::Newtonsoft.Json;
global using global::Newtonsoft.Json.Linq;
global using global::Prometheus;
global using global::Serilog;
global using global::Swashbuckle.AspNetCore.Annotations;
global using global::System;
global using global::System.Collections.Generic;
global using global::System.Globalization;
global using global::System.IO;
global using global::System.Linq;
global using global::System.Net.Http;
global using global::System.Net.Http.Json;
global using global::System.Reflection;
global using global::System.Threading;
global using global::System.Threading.Tasks;
global using global::commercetools.Base.Client;
global using global::commercetools.Sdk.Api;
global using global::commercetools.Sdk.Api.Extensions;
global using global::commercetools.Sdk.Api.Serialization;
global using HttpClient = global::ITF.SharedLibraries.HttpClient.HttpClient;
global using static global::ITF.Lib.Common.Notifications.Tracing;
global using static global::ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;
global using static global::ITF.SharedLibraries.ExtensionMethods.Serializer;
global using static global::ITF.SharedLibraries.HttpClient.Polly.Extensions;
global using static global::ITF.SharedLibraries.Kafka.DelegateHandler;
