#!/bin/bash
set -e
### Create all azure devops envs for a repo ###


#####
# Manage script arguments and help
#####
print_usage () {
  echo ""
  echo "======================"
  echo "Usage of this script:"
  echo "======================"
  echo "mandatory args:"
  echo "  -r|--repo Repo name (like IT.Microservices.Stuff.Doodler)"
  echo "  -c|--contry list of contry acronym separated by coma (like itf,iti,ite)"
  # echo "  -o|--org Azure orguanisation namei (like OCTO-MS)"
  # echo "  -u|--user Azure user mail"
  # echo "  -p|--password Azure user password"
  echo "optional args:"
  echo "  --help"
}

print_naming_error () {
  echo ""
  echo "======================"
  echo "Naming error on ${1}"
  echo "======================"
  echo "Error:"
  echo "${2}"
  echo "${3}"
  echo ""
}

POSITIONAL_ARGS=()
while [[ $# -gt 0 ]]; do
  case $1 in
    # -u|--user)
    #   USER="$2"
    #   shift # past argument
    #   shift # past value
    #   ;;
    # -p|--password)
    #   PASSWORD="$2"
    #   shift # past argument
    #   shift # past value
    #   ;;
#    -o|--org)
#      ORG="$2"
#      shift # past argument
#      shift # past value
#      ;;
    -r|--repo)
      REPO="$2"
      shift # past argument
      shift # past value
      ;;
    -c|--contry)
      IFS=',' read -ra CTRY <<< "$2"
      shift # past argument
      shift # past value
      ;;
    -t|--type)
      IFS=',' read -ra TYPE <<< "$2"
      shift # past argument
      shift # past value
      ;;
    --help)
      HELP=YES
      shift # past argument
      ;;
    -*|--*)
      echo "Unknown option $1"
      print_usage
      exit 1
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done
set -- "${POSITIONAL_ARGS[@]}" # restore positional parameters

# Note: for now this only works for microservices, so ORG is fixed to OCTO-MS
# if we want it to work for other context we should at least allow the user to
# choose kub namespaces attached to those env, for now the namepsaces are
# hard-coded in the curl command.
ORG="OCTO-MS"
USER="${AZDEVOPS_USER}"
PASSWORD="${AZDEVOPS_PASSWORD}"

if [[ -n ${HELP} ]]; then
  print_usage
  exit 0
elif [[  -z "${CTRY}" || -z "${TYPE}" || -z "${REPO}" || -z "${ORG}" || -z "${USER}" || -z "${PASSWORD}" ]]; then
  print_usage
  exit 1
fi

# Define standardized short name
SHORT_NAME=$(echo ${REPO} | sed -e "s/^\(IT[^.]*\)\.Microservices\./\1/g" | sed -e "s/\.//g" | tr "[:upper:]" "[:lower:]")

# Login
az login --allow-no-subscriptions -u ${USER} -p ${PASSWORD}
SCOPE='499b84ac-1321-427f-aa17-267ca6975798/.default' # 499b84ac... being Azure Devops app id
ACCESS_TOKEN=$(az account get-access-token --scope ${SCOPE} | jq -r .accessToken)
EXP_DATE=$(date '+%Y-%m-%dT%H:%M:%S:%s.0000000Z' -d "+2 minutes")
TOKEN_NAME="tmp-pat-$(echo ${EXP_DATE} | md5sum -t | cut -c1-8)"
TOKEN=$(curl -H "Authorization: Bearer ${ACCESS_TOKEN}" -H "Content-Type: application/json" -XPOST "https://vssps.dev.azure.com/OCTO-MS/_apis/tokens/pats?api-version=7.0-preview.1" -d "{\"displayName\": \"${TOKEN_NAME}\", \"scope\": \"app_token\", \"validTo\": \"${EXP_DATE}\", \"allOrgs\": true}" | jq -r .patToken.token)
export AZURE_DEVOPS_EXT_PAT=${TOKEN}


#####
# Create envs
#####
echo -e "\n\n"
echo "=============================="
echo "Creating envs for ${REPO}"
echo "=============================="
echo -e "\n\n"

existing_environements=$(curl -s -u "${USER}:${TOKEN}" "https://dev.azure.com/${ORG}/${REPO}/_apis/distributedtask/environments?api-version=7.0")
for env in dev recette perf preprod prod; do
      for ctry in "${CTRY[@]}"; do
            this_environement=$(echo $existing_environements | jq -r " .value[] | select(.name == \"${env}-${ctry}-${SHORT_NAME}\")")
            if [ "$this_environement" ]; then
                  echo "Already existing env ${env}-${ctry}-${SHORT_NAME}"
            else
                  echo "Creating env ${env}-${ctry}-${SHORT_NAME}"
                  curl -s -u "${USER}:${TOKEN}" "https://dev.azure.com/${ORG}/${REPO}/_apis/distributedtask/environments?api-version=6.0-preview.1" -X POST -H "Content-Type: application/json" -d "{\"name\": \"${env}-${ctry}-${SHORT_NAME}\", \"description\": \"Manually created env\"}" | grep -v Exception 1>/dev/null
            fi
      done
done


#####
# Create service-endpoint (aka service-connection)
#####
echo -e "\n\n"
echo "=============================="
echo "Create service-endpoints for ${REPO} (aka service-connection)"
echo "=============================="
echo -e "\n\n"

# Note: the following part needs previously created service principals "azuredevops_pipelines_${env}",
# with "Contributor" role given to itf-{env}-k8s-rg given through Access Control (IAM) 
existing_endpoints=$(az devops service-endpoint list --org "https://dev.azure.com/${ORG}" --project "${REPO}")
for env in dev recette perf preprod prod; do
      this_endpoint=$(echo $existing_endpoints | jq -r ".[] | select (.name == \"it-${env}-cicd-sp\")")
      # test if this endpoint already exist
      if [ "$this_endpoint" ]; then
            echo "Already existing service endpoint it-${env}-cicd-sp"
      else
            echo "Creating service connection it-${env}-cicd-sp"
            # get pre-made service principal id
            sp_id=$(az ad sp list --filter "displayName eq 'azuredevops_pipelines_${env}'" | jq -r ".[].appId")
            #create secret with "az ad sp credential" and get it's value, unless $<ENV>_SP_SECRET exist
            secret_var="SP_SECRET_${env^^}"
            if [ ${!secret_var} ]; then
                  export AZURE_DEVOPS_EXT_AZURE_RM_SERVICE_PRINCIPAL_KEY=${!secret_var}
            else
                  export AZURE_DEVOPS_EXT_AZURE_RM_SERVICE_PRINCIPAL_KEY=$(az ad app credential reset --id "${sp_id}" --append --display-name "${REPO}-it-${env}-cicd-sp"| jq -r .password)
            fi
            # create service endpoint it-{env}-cicd-sp
            az devops service-endpoint azurerm create  --name "it-${env}-cicd-sp" --org "https://dev.azure.com/${ORG}" --project "${REPO}" --azure-rm-subscription-id ac825c00-eca9-4ea2-a849-9db5eb194657 --azure-rm-subscription-name OCTOPUS --azure-rm-service-principal-id "${sp_id}" --azure-rm-tenant-id ae228da7-1ce3-40d8-9cad-6324f71f9b1c 1>/dev/null
      fi
done


#####
# Create pipelines
#####
echo -e "\n\n"
echo "=============================="
echo "Create pipelines for ${REPO}"
echo "=============================="
echo -e "\n\n"

if [ ${ACR_CRED_SHARED} ]; then
      HelmChartRepositoryClientSecret=${ACR_CRED_SHARED}
else
      HelmChartRepositoryClientSecret=$(az storage blob download --container-name tfstates --account-name itftfstates --name sharedinfra.terraform.tfstate 2>/dev/null | jq -r '.resources[] | select(.name=="acr_helmfile" and .type=="random_password") | .instances[0].attributes.result')
fi
for env in dev recette perf preprod prod; do
      # test if pipeline already exist
      if az pipelines show --name "${env}-pipeline" --org "https://dev.azure.com/${ORG}" --project "${REPO}" 1>/dev/null 2>/dev/null ; then
            echo "Already existing pipeline ${env}-pipeline"
      else
            echo "Creating pipeline ${env}-pipeline"
            # create pipeline
            az_pipeline_infos=$(az pipelines create --name "${env}-pipeline" --branch "master" --yaml-path "/cicd/cicd-pipelines/${env}-pipeline.yml" --org "https://dev.azure.com/${ORG}" --project "${REPO}" --repository "${REPO}" --repository-type "TfsGit" --skip-first-run)
            pipeline_id=$(echo ${az_pipeline_infos} | jq -r .id )
            # add variable to pipeline
            acr_cred_var="ACR_CRED_${env^^}"
            if [ ${!acr_cred_var} ]; then
                  export containerRegistryPwd=${!acr_cred_var}
            else
                  export AZURE_DEVOPS_EXT_AZURE_RM_SERVICE_PRINCIPAL_KEY=$(az ad app credential reset --id "${sp_id}" --append --display-name "${REPO}-it-${env}-cicd-sp"| jq -r .password)
                  export containerRegistryPwd=$(az acr credential show -n "itf${env}acr" | jq -r .passwords[0].value)
            fi
            az pipelines variable create --name "containerRegistryPwd" --value "${containerRegistryPwd}" --org "https://dev.azure.com/${ORG}" --project "${REPO}" --pipeline-name "${env}-pipeline" --pipeline-id "${pipeline_id}" --secret true --allow-override true 1>/dev/null
            az pipelines variable create --name "HelmChartRepositoryClientSecret" --value "${HelmChartRepositoryClientSecret}" --org "https://dev.azure.com/${ORG}" --project "${REPO}" --pipeline-name "${env}-pipeline" --pipeline-id "${pipeline_id}" --secret true --allow-override true 1>/dev/null
      fi
done

#####
# Set Pipeline Permission
#####
echo -e "\n\n"
echo "=============================="
echo "Set Pipeline Permissions for ${REPO}"
echo "=============================="
echo -e "\n\n"

all_project_pipelines=$(az pipelines list --org "https://dev.azure.com/${ORG}" --project "${REPO}")
all_project_environements=$(curl -s -u "${USER}:${TOKEN}" "https://dev.azure.com/${ORG}/${REPO}/_apis/distributedtask/environments?api-version=7.0" )
all_project_endpoints=$(az devops service-endpoint list --org "https://dev.azure.com/${ORG}" --project "${REPO}")
for env in dev recette perf preprod prod; do
      echo "set permission on endpoint it-${env}-cicd-sp..."
      pipeline_id=$(echo $all_project_pipelines | jq -r " .[] | select(.name == \"${env}-pipeline\") | .id")
      endpoint_id=$(echo $all_project_endpoints | jq -r ".[] | select(.name == \"it-${env}-cicd-sp\") | .id")
      curl -s -u "${USER}:${TOKEN}" -XPATCH "https://dev.azure.com/${ORG}/${REPO}/_apis/pipelines/pipelinepermissions/endpoint/${endpoint_id}?api-version=7.0-preview.1"  -H "Content-Type: application/json" -d "{ \"pipelines\": [ { \"id\": ${pipeline_id}, \"authorized\": true }] }" | grep -v Exception 1>/dev/null
      for ctry in "${CTRY[@]}"; do
              echo "set permission on environment ${env}-${ctry}-${SHORT_NAME}..."
              environment_id=$(echo $all_project_environements | jq -r " .value[] | select(.name == \"${env}-${ctry}-${SHORT_NAME}\") | .id")
              curl -s -u "${USER}:${TOKEN}" -XPATCH "https://dev.azure.com/${ORG}/${REPO}/_apis/pipelines/pipelinepermissions/environment/${environment_id}?api-version=7.0-preview.1"  -H "Content-Type: application/json" -d "{ \"pipelines\": [ { \"id\": ${pipeline_id}, \"authorized\": true }] }" | grep -v Exception 1>/dev/null
      done
done



# set approval checking for prod pipeline
prod_pipeline_id=$(echo $all_project_pipelines | jq -r " .[] | select(.name == \"prod-pipeline\") | .id")
prod_environements_ids=$(echo $all_project_environements | jq ".value[] | select(.name | test(\"^prod\")) | .id")
team_group_id=$(az devops security group list --org https://dev.azure.com/OCTO-MS  --project "${REPO}" | jq -r ".graphGroups[] | select (.displayName == \"${REPO} Team\") | .originId ") 



for this_prod_pipeline_id in $prod_environements_ids; do
curl_res=$(curl -s -u "${USER}:${TOKEN}" -XPOST "https://dev.azure.com/${ORG}/${REPO}/_apis/pipelines/checks/configurations?api-version=7.0-preview.1" -H "Content-Type: application/json" -o .curl_res --data-binary @- <<EOF
{
  "settings": {
    "approvers": [
      {
        "displayName": "${REPO} Team",
        "id": "${team_group_id}"
      }
    ],
    "minRequiredApprovers": 1,
    "executionOrder": "anyOrder"
  },
  "type": {
    "id": "8c6f20a7-a545-4486-9777-f762fafe0d4d",
    "name": "Approval"
  },
  "resource": {
    "type": "environment",
    "id": "${this_prod_pipeline_id}"
  }
}
EOF
)
echo "${curl_res}" | grep -v Exception 1>/dev/null
done

#####
# Create helm chart
#####
echo -e "\n\n"
echo "=============================="
echo "Create helm chart for ${REPO}"
echo "=============================="
echo -e "\n\n"


git clone https://${TOKEN}:@dev.azure.com/OCTO-OPS/ITF.APP.HELM/_git/charts charts_repo --quiet
git -C charts_repo config credential.helper store --quiet
git -C charts_repo config user.email "<EMAIL>" --quiet
git -C charts_repo config user.name "admindevops" --quiet
if [ "$(grep -x "name: ${SHORT_NAME}" charts_repo/src/${REPO}/Chart.yaml 2>/dev/null)" ]; then
      echo "Already existing chart for repo ${REPO}"
elif [ "$(grep -x "name: ${SHORT_NAME}" charts_repo/src/*/Chart.yaml 2>/dev/null)" ]; then
      repo_using_chart="$(grep -x "name: ${SHORT_NAME}" charts_repo/src/*/Chart.yaml | cut -d'/' -f3)"
      echo "ERROR: Already existing chart named ${SHORT_NAME}, but for ${repo_using_chart}"
      exit 1
else
      git -C charts_repo checkout -b ms_pipeline_${SHORT_NAME} --quiet
      if [ "${TYPE}" == "application" ]; then
            cp -rf charts_repo/template_helm_chart_microservice charts_repo/src/${REPO} 1>/dev/null
      elif [ "${TYPE}" == "cronjob" ]; then
            cp -rf charts_repo/template_helm_chart_microservice_cron charts_repo/src/${REPO} 1>/dev/null
      fi
      sed -i charts_repo/src/${REPO}/*.yaml -e "s/myapplicationname/${SHORT_NAME}/g" 1>/dev/null
      sed -i charts_repo/src/${REPO}/*.yaml -e "s/myreponame/${REPO}/g" 1>/dev/null
      sed -i charts_repo/src/${REPO}/Chart.yaml -e "s/description: Quick description of your application/description: ${REPO}/g" 1>/dev/null
      git -C charts_repo add src/${REPO}/*
      git -C charts_repo commit -m "first chart for ${REPO} from template (from TIF.Microservices pipeline)" --quiet
      git -C charts_repo push --set-upstream origin ms_pipeline_${SHORT_NAME} --quiet
      az repos pr create --organization https://dev.azure.com/OCTO-OPS --project ITF.APP.HELM --repository charts --description "first chart for ${REPO} from template (from TIF.Microservices pipeline)" --source-branch ms_pipeline_${SHORT_NAME} --target-branch master --reviewers "<EMAIL>" "<EMAIL>" --title "Infra: New chart for ${REPO} names ${SHORT_NAME}" 1>/dev/null --auto-complete
      echo "Done."
fi