﻿namespace ITF.Microservices.Template.Reactor.MyHandler;

public interface IMyFacade
{
    public Task Process(object? data, string? topic = null, int? partition = null, long? offset = null);
}

public class MyFacade(
    IMyUseCase myUseCase,
    IFeatureFlags featureManager,
    SerializerService serializerService,
    ILogger<MyFacade> logger) : IMyFacade
{
    private async Task MyUseCaseSpecificProcess(ProductPublishedMessage message, string? topic = null, int? partition = null, long? offset = null)
    {
        // Do your tracing / tagging / logging / retry policy / feature flag checks here
        await Agent.Tracer
          .CaptureTransaction($"{nameof(MyUseCaseSpecificProcess)}_{nameof(ProductPublishedMessage)}Transaction", "Kafka", async (t) =>
          {
              // Tag the transaction with relevant properties
              TagTransaction(t, "Topic", topic);
              TagTransaction(t, "Partition", partition);
              TagTransaction(t, "Offset", offset);
              TagTransaction(t, "CategoryReference", message?.GetMessageKey());
              TagTransaction(t, "CausationId", message?.CausationId);

              // Check any feature flag if relevant
              if (!await featureManager.IsEnabled("IsMyUseCaseFeatureEnabled"))
              {
                  logger.LogWarning("Feature XXX is disabled, process skipped");
                  return;
              }

              // Make some standard log (on typed objects)
              logger.LogInformation("Process {process} on message {message} for product reference {productReference}", nameof(MyUseCaseSpecificProcess), nameof(ProductPublishedMessage), message?.GetMessageKey());

              // Apply a polly policy to your underlying process if required
              await PollyPolicyFactory.GetAsyncPolicy<Exception>(logger).ExecuteAsync(async () =>
              {
                  await myUseCase.SychronizeProcess(message!);
              });
          });
    }

    public async Task Process(object? data, string? topic = null, int? partition = null, long? offset = null)
    {
        try
        {
            var result = data switch
            {
                ProductPublishedMessage msg => MyUseCaseSpecificProcess(msg, topic, partition, offset),

                _ => Task.CompletedTask
            };

            await result;
        }
        catch (Exception e)
        {
            var stopHandler = false;
            await Agent.Tracer
            .CaptureTransaction($"ERR_KafkaExceptionHandlingTransaction", "Kafka", async (t) =>
            {
                TagTransaction(t, "Topic", topic);
                TagTransaction(t, "Partition", partition);
                TagTransaction(t, "Offset", offset);
                var (productReference, category, causationId) = GetInformations(data);
                TagTransaction(t, "ProductReference", productReference);
                TagTransaction(t, "Category", category);
                TagTransaction(t, "CausationId", causationId);

                logger.LogError(e, "Failed to apply {eventType} event with {payload} for product reference {productReference} from Kafka with exception message : {error}", data?.GetType()?.Name, data?.Serialize(SerializerType.CommerceTools, serializerService), productReference, e.Message);

                stopHandler = await NeedShutdownAsync(e);

                if (stopHandler)
                    logger.LogCritical(e, "Handler stopped !");

            }, DistributedTracingData.TryDeserializeFromString(GetTracingData(data)));

            if (stopHandler)
            {
#if !DEBUG
                await Task.Delay(5000); // To flush APM transactions
                throw;
#endif
            }
        }
    }

    private async Task<bool> NeedShutdownAsync(Exception e)
        => await featureManager.IsEnabled("ShutdownOnException");

    private static (string? productReference, string? category, string? causationId) GetInformations(object data)
        => data switch
        {
            ProductPublishedMessage msg => (msg.GetMessageKey(), null, msg.CausationId),
            _ => (null, null, null),
        };
}
