# Templates

Templates for Microservices applications and libraries.

## Install templates locally
### Standard project 
`dotnet new -i "[... fullpath...]\ITF.Microservices\scripts\templates.net\StandardProject"`

or using relative paths (depending of the current path)
`dotnet new -i .\StandardProject\`

### Reactor project 
`dotnet new -i "[... fullpath...]\ITF.Microservices\scripts\templates.net\ReactorProject"`

or using relative paths (depending of the current path)
`dotnet new -i .\ReactorProject\`

### Library project 
`dotnet new -i "[... fullpath...]\ITF.Microservices\scripts\templates.net\LibraryProject"`

or using relative paths (depending of the current path)
`dotnet new -i .\LibraryProject\`

**Warning** : **Dont forget to allow the buildService team into the Feed settings for library project into azure dev ops : see the doc on confluence : <https://myflower.atlassian.net/wiki/spaces/MSA/pages/1338703893/Create+a+Microservice+project#For-Library-Project-%3A>**


## Create a new project based on a template
Place into the expected output directory

### Standard and reactor projects
You may specify the required version for the sharedlib or ignore the parameter to use the version specified in the template. The same goes for the unleash key or other optional parameters.

`dotnet new itfms -n ITF.Microservices.[NameOfProject]`
See all available parameters with the -h argument on itfms template

`dotnet new itfmsreactor -n ITF.Microservices.[NameOfProject]`
See all available parameters with the -h argument on itfmsreactor template

### Library project 
`dotnet new itfmslib -n ITF.Microservices.[NameOfProject]`
See all available parameters with the -h argument on itfmslib template

## List all templates
`dotnet new --list`

## Get parameters for a template
`dotnet new itfms -h`