﻿namespace ITF.Microservices.Template.Api.MyFeature;

public record MyRequest(string FirstName = "", string LastName = "");

public record MyResponse
{
    public string FullName { get; init; } = string.Empty;
    public bool IsValid { get; init; } = false;
}

public class MyFeatureController(ILogger<MyFeatureController> logger , IMyUseCase myUseCase) : BaseController
{
    [SwaggerOperation(
        Summary = "",
        Description = "",
        OperationId = "MyFeature")]
    [SwaggerResponse(200, "", typeof(MyResponse))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occured, please contact us")]
    [HttpPost("")]
    public IActionResult MyFeature([FromBody] MyRequest req)
    {
        logger.LogWarning("Request received : {req}", req.Serialize());
        var res = myUseCase.Process(req);
        return Ok(new MyResponse { 
            FullName = req.FirstName + req.LastName ,
            IsValid = res.IsSuccess && res.Value
        });
    }
}
