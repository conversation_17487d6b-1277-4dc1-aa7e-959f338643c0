#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
ITF.Lib.Common
ITF.SharedLibraries
ITF.SharedModels
IT.SharedLibraries.CT
SE.CourierLib.GLS
SE.CourierLib.Qapla
SE.CourierLib.Paack
IT.CourierLib.Shared
ITE.Order.Library
ITF.Order.Library
ITI.Order.Library
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  <NAME_EMAIL>:v3/interflorad365fo/$REPOSITORY/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-<NAME_EMAIL>:v3/interflorad365fo/$REPOSITORY/$REPOSITORY
  git -C $REPOSITORY pull
done
  
REPOSITORIESOCTO=(
IT.Microservices.CT.Order.Wrapper
IT.Microservices.CourrierWrapper
IT.Microservices.KafkaToHttpReactor
IT.Microservices.OrderReactor
IT.Microservices.Shipment
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  <NAME_EMAIL>:v3/OCTO-MS/$REPOSITORY/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-<NAME_EMAIL>:v3/OCTO-MS/$REPOSITORY/$REPOSITORY 
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done
