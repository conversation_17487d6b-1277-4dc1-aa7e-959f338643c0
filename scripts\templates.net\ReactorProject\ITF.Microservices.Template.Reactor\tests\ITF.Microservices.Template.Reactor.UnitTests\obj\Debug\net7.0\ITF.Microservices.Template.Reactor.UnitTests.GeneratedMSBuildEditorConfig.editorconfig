is_global = true
build_property.TargetFramework = net7.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = ITF.Microservices.Template.Reactor.UnitTests
build_property.ProjectDir = C:\MS\scripts\templates.net\ReactorProject\ITF.Microservices.Template.Reactor\tests\ITF.Microservices.Template.Reactor.UnitTests\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
