﻿global using static ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Product.Messages.V1;

namespace ITF.Microservices.Template.Reactor.MyHandler;

public class MyHandler(IServiceScopeFactory scopeFactory) : KafkaBaseMessageHandler
{
    public override async Task HandleMessage(object data, string topic = null, int? partition = null, long? offset = null)
    {
        var myFacade = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMyFacade>();

        var result = data switch
        {
            ProductPublishedMessage d => myFacade.Process(d, topic, partition, offset),
            _ => Task.CompletedTask
        };

        await result;
    }
}
