﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Template.Api", "StandardProject\ITF.Microservices.Template.Api\src\ITF.Microservices.Template.Api.csproj", "{5BAE5403-7FAF-481B-9122-CD6ED8760FBA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Template.Api.UnitTests", "StandardProject\ITF.Microservices.Template.Api\tests\ITF.Microservices.Template.Api.UnitTests\ITF.Microservices.Template.Api.UnitTests.csproj", "{0F75443A-DA58-46BD-BC6E-9A22896C8FA6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Template.Reactor", "ReactorProject\ITF.Microservices.Template.Reactor\src\ITF.Microservices.Template.Reactor.csproj", "{AA8F6B91-1378-4563-9534-B8C992DE2E59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Template.Reactor.UnitTests", "ReactorProject\ITF.Microservices.Template.Reactor\tests\ITF.Microservices.Template.Reactor.UnitTests\ITF.Microservices.Template.Reactor.UnitTests.csproj", "{5E2FFA5F-2C06-4947-98D7-2ACBF4B58751}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Template.Lib", "LibraryProject\ITF.Microservices.Template.Lib\src\ITF.Microservices.Template.Lib.csproj", "{A70DF43B-AAE8-4DEB-AB36-89F4AF3730DA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Microservices.Template.Lib.UnitTests", "LibraryProject\ITF.Microservices.Template.Lib\tests\ITF.Microservices.Template.Lib.UnitTests\ITF.Microservices.Template.Lib.UnitTests.csproj", "{DCA4BBB9-E829-4FD3-8F58-AA753F359A7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedLibraries", "..\..\src\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj", "{E59F3FDA-BF02-497A-BA22-4A7C552C483F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.Lib.Common", "..\..\src\ITF.Lib.Common\src\ITF.Lib.Common.csproj", "{6D31AF5E-0447-4683-9932-EC6401049721}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ITF.SharedModels", "..\..\src\ITF.SharedModels\src\ITF.SharedModels.csproj", "{8683B60A-9D3E-49B5-A3B9-0F0C3EDEF4D9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5BAE5403-7FAF-481B-9122-CD6ED8760FBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5BAE5403-7FAF-481B-9122-CD6ED8760FBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5BAE5403-7FAF-481B-9122-CD6ED8760FBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5BAE5403-7FAF-481B-9122-CD6ED8760FBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{0F75443A-DA58-46BD-BC6E-9A22896C8FA6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F75443A-DA58-46BD-BC6E-9A22896C8FA6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F75443A-DA58-46BD-BC6E-9A22896C8FA6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F75443A-DA58-46BD-BC6E-9A22896C8FA6}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA8F6B91-1378-4563-9534-B8C992DE2E59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA8F6B91-1378-4563-9534-B8C992DE2E59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA8F6B91-1378-4563-9534-B8C992DE2E59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA8F6B91-1378-4563-9534-B8C992DE2E59}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E2FFA5F-2C06-4947-98D7-2ACBF4B58751}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E2FFA5F-2C06-4947-98D7-2ACBF4B58751}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E2FFA5F-2C06-4947-98D7-2ACBF4B58751}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E2FFA5F-2C06-4947-98D7-2ACBF4B58751}.Release|Any CPU.Build.0 = Release|Any CPU
		{A70DF43B-AAE8-4DEB-AB36-89F4AF3730DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A70DF43B-AAE8-4DEB-AB36-89F4AF3730DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A70DF43B-AAE8-4DEB-AB36-89F4AF3730DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A70DF43B-AAE8-4DEB-AB36-89F4AF3730DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCA4BBB9-E829-4FD3-8F58-AA753F359A7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCA4BBB9-E829-4FD3-8F58-AA753F359A7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCA4BBB9-E829-4FD3-8F58-AA753F359A7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCA4BBB9-E829-4FD3-8F58-AA753F359A7D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E59F3FDA-BF02-497A-BA22-4A7C552C483F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E59F3FDA-BF02-497A-BA22-4A7C552C483F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E59F3FDA-BF02-497A-BA22-4A7C552C483F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E59F3FDA-BF02-497A-BA22-4A7C552C483F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D31AF5E-0447-4683-9932-EC6401049721}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D31AF5E-0447-4683-9932-EC6401049721}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D31AF5E-0447-4683-9932-EC6401049721}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D31AF5E-0447-4683-9932-EC6401049721}.Release|Any CPU.Build.0 = Release|Any CPU
		{8683B60A-9D3E-49B5-A3B9-0F0C3EDEF4D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8683B60A-9D3E-49B5-A3B9-0F0C3EDEF4D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8683B60A-9D3E-49B5-A3B9-0F0C3EDEF4D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8683B60A-9D3E-49B5-A3B9-0F0C3EDEF4D9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C3258425-25AA-4D52-914B-E275BCC2DDF6}
	EndGlobalSection
EndGlobal
