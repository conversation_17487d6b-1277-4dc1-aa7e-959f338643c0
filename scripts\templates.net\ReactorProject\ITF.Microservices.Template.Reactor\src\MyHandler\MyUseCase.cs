﻿using static ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Product.Messages.V1;

namespace ITF.Microservices.Template.Reactor.MyHandler;

public interface IMyUseCase
{
    Task SychronizeProcess(ProductPublishedMessage message);
}

public class MyUseCase(ILogger<MyUseCase> logger) : IMyUseCase
{
    public Task SychronizeProcess(ProductPublishedMessage message)
    {
        // Do your business logic implementation
        throw new NotImplementedException();
    }
}
