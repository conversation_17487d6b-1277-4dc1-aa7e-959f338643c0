{"author": "Dorian GERAUT", "classifications": [".NET core", "Microservices"], "name": "Interflora .Net Core Microservice project", "identity": "ITF.MS.CRONJOB", "shortName": "itfmscronjob", "tags": {"language": "C#"}, "sourceName": "ITF.Microservices.Template.Cronjob", "symbols": {"sharedlib": {"type": "parameter", "datatype": "string", "replaces": "SHAREDLIB_FRAMEWORK_VERSION", "defaultValue": "8.0.1", "description": "The shared library version to use."}, "imageContainerName": {"type": "derived", "valueSource": "name", "valueTransform": "imageContainerNameForm", "replaces": "IMAGE_CONTAINER_NAME", "description": "The name of the docker container image. Should not contains any special characters and in lowercase"}, "schedule": {"type": "parameter", "datatype": "string", "replaces": "SCHEDULE", "defaultValue": "0 1 * * *", "description": "<PERSON><PERSON> schedule expression for this job"}, "helmVersion": {"type": "parameter", "datatype": "string", "replaces": "HELM_VERSION", "defaultValue": "3.7.1", "description": "Define helm version."}, "helmChartVersion": {"type": "parameter", "datatype": "string", "replaces": "HELM_CHART_VERSION", "defaultValue": "1.0.0", "description": "Define helm chart version."}}, "forms": {"imageContainerNameForm": {"identifier": "chain", "steps": ["removeNameStart", "removeDots", "lowerCase"]}, "removeNameStart": {"identifier": "replace", "pattern": "\\.Microservices\\.", "replacement": ""}, "removeDots": {"identifier": "replace", "pattern": "\\.", "replacement": ""}}}