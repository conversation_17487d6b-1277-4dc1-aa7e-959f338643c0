#!/bin/bash

cd ../src

REPOSITORIES=(ITF.SharedLibraries ITF.SharedModels IT.SharedLibraries.CT)
REPOSITORIES+=(IT.Microservices.CT.Order.Wrapper ITF.Microservices.NotificationSupplier IT.Microservices.Florist)
for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  cd $REPOSITORY
  git pull
  cd ../
done