{"ElasticApm": {"ServerUrl": "http://apm:8200"}, "ElasticSearchLog": {"ElasticSearchLog": "http://elasticsearch:9200/"}, "Unleash": {"Url": "http://unleash:4242/api/"}, "FeatureFlags": {"Provider": "featuremanager"}, "Kafka": {"BootStrapServers": "kafka:9092", "SubscriberConfigurations": [{"AutoOffsetReset": 1, "ClassName": "<PERSON><PERSON><PERSON><PERSON>", "EnableAutoCommit": false, "ManualCommitPeriod": 1, "EnablePartitionEof": false, "GroupId": "ITF.Microservices.Template.Reactor.consumer", "TopicName": "myTopic"}]}, "FeatureManagement": {"ShutdownOnException": false}}