# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# V.C 17/05/2024

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/ITF.Microservices.Template.Reactor.csproj", "/src/ITF.Microservices.Template.Reactor/"]
COPY ["src/NuGet.config", "/src/ITF.Microservices.Template.Reactor/"]

RUN dotnet restore "ITF.Microservices.Template.Reactor/ITF.Microservices.Template.Reactor.csproj"

WORKDIR "/src/ITF.Microservices.Template.Reactor"

COPY . .

RUN dotnet build "src/ITF.Microservices.Template.Reactor.csproj" -c Release
RUN dotnet test "tests/ITF.Microservices.Template.Reactor.UnitTests/ITF.Microservices.Template.Reactor.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/ITF.Microservices.Template.Reactor.csproj" -c Release -o out

ENTRYPOINT sleep 10000