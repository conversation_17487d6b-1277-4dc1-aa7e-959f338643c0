###
# Values for preprod environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/ITF.Microservices.Template.Api/"
#helm diff upgrade IMAGE_CONTAINER_NAME ${helmChartPath} --values chart-values/values-preprod-portugal.yaml -n itp-ms --set 'image.tag=latest,image.repository=itppreprodacr.azurecr.io/IMAGE_CONTAINER_NAME'

replicaCount: REPLICA_COUNT

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "IMAGE_CONTAINER_NAME"
fullnameOverride: "IMAGE_CONTAINER_NAME"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "itp-ms-IMAGE_CONTAINER_NAME"

dotnetProgramName: "ITF.Microservices.Template.Api.dll"
appStartCommand: "dotnet ITF.Microservices.Template.Api.dll"

podAnnotations: []
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/IMAGE_CONTAINER_NAME.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "itp-microservices"
  # vault.hashicorp.com/agent-inject-secret-IMAGE_CONTAINER_NAME.pass: "applications/itp-microservices"

  # Inject secret via a configmap named IMAGE_CONTAINER_NAME-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'IMAGE_CONTAINER_NAME-secrets'
  
  #inject secret via env variables 
  #vault.hashicorp.com/agent-inject: 'true'
  #vault.hashicorp.com/role: 'itp-microservices'
  #vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/itp-microservices'
  #vault.hashicorp.com/agent-inject-template-config: |

service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.preprod.interflora.pt
  path: "/IMAGE_CONTAINER_NAME"
  tls:
  - hosts:
    - microservices.preprod.interflora.pt
    secretName: "preprod-interflora-pt-cert"  
  enabled: true
  ingressClass: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://*.interflora.pt"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow ************/32;
      allow *************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/IMAGE_CONTAINER_NAME$ /IMAGE_CONTAINER_NAME/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

livenessProbe:
  httpGet:
    path: /health
    port: 80
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  
autoscaling:
  enabled: AUTO_SCALING_ENABLED
  minReplicas: MIN_REPLICA
  maxReplicas: MAX_REPLICA
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 100

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 
  serilogConfig: |-       
    "Serilog": {
      "Using": [],
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "Microsoft": "Warning",
          "System": "Information",
          "Elastic": "Warning",
          "Apm": "Warning"
        }
      },
      "WriteTo": [
        {
          "Name": "Console"
        }
      ],
      "Enrich": [
        "FromLogContext",
        "WithMachineName",
        "WithProcessId",
        "WithThreadId"
      ],
      "Properties": {
        "ApplicationName": "itp-IMAGE_CONTAINER_NAME"
      }
    }

env:
  - name: MongoDb__ConnectionString
    value: "mongodb://itp-ms-mongodb-0.itp-ms-mongodb-headless.itp-ms-common.svc.cluster.local:27017,itp-ms-mongodb-1.itp-ms-mongodb-headless.itp-ms-common.svc.cluster.local:27017,itp-ms-mongodb-2.itp-ms-mongodb-headless.itp-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: ElasticApm__Environment
    value: "itp-preprod"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "itp-IMAGE_CONTAINER_NAME"    

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "itp-ms-kafka-headless.itp-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "itp-ms-redis-headless.itp-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://itp-unleash.itp-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "itp-IMAGE_CONTAINER_NAME"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "production"