trigger:
- master

pool:
  vmImage: ubuntu-latest

variables:
  #####################################
  ## common-variables
  #####################################

  projectName: 'IMAGE_CONTAINER_NAME'

  #Use by csproj files to build and run unit tests
  dotnetProjectName: 'ITF.Microservices.Template.Reactor'
  dotnetTestName: 'ITF.Microservices.Template.Reactor.UnitTests'

  helmVersion: HELM_VERSION
  HELM_EXPERIMENTAL_OCI: '1'

  #Must match chart/Chart.yaml name
  helmChartName: 'IMAGE_CONTAINER_NAME'

  #Helm Release name 
  helmReleaseName: 'IMAGE_CONTAINER_NAME'

  #Use Azure devOps build id to tag image --> must be remplaced by commitID 
  imageTag: $(build.SourceVersion)

  #Registry name to store docker image and helm chart (create if not exist, must be lowercase)
  imageRepository: 'IMAGE_CONTAINER_NAME'

  #####################################
  ##dev-variables
  #####################################

  #According to chart/Chart.yaml value   
  helmChartVersion: "HELM_CHART_VERSION"
  
  containerRegistry: 'itfdevacr.azurecr.io'
  containerFullPath: '$(containerRegistry)/$(imageRepository)'
  containerRegistryLogin: 'itfdevacr'
  #containerRegistryPwd: 'must be defined in azure pipeline variables'
    
  #Azure service connection name(Project settings -> Service connection -> Azure Resource Manager)
  Azure.ServiceConnection: 'it-dev-cicd-sp'

  Azure.resourceGroup: 'itf-dev-k8s-rg'
  Azure.kubernetesClusterName: 'itf-dev-k8s-aks'

  #Shared Helm chart repository name (output from terraform)
  HelmChartRepository: 'itfsharedacr'
  HelmChartRepositoryClientID: "70085f74-d6cc-4bda-8cd1-bad624af5a20"
  #HelmChartRepositoryClientSecret: 'must be defined in azure pipeline variables'
  
  #Enable Azure devops debug mode
  System.Debug: 'false'

stages:
- stage: Build_Stage
  displayName: Build image
  jobs:  
  - job: Build_Job
    displayName: Build and push Docker/Helm image
    steps: 
    - template: templates/build.yml

- stage: Deploy_France
  dependsOn: Build_Stage
  displayName: Deploy IMAGE_CONTAINER_NAME France (k8s)
  jobs: 
  - deployment: Deploy_France
    displayName: Deploy France app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-france.yaml"
      K8S.Namespace: 'itf-ms'
    environment: dev-itf-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Italy
  dependsOn: Build_Stage
  displayName: Deploy IMAGE_CONTAINER_NAME Italy (k8s)
  jobs: 
  - deployment: Deploy_Italy
    displayName: Deploy Italy app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-italy.yaml"
      K8S.Namespace: 'iti-ms'
    environment: dev-iti-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Spain
  dependsOn: Build_Stage
  displayName: Deploy IMAGE_CONTAINER_NAME Spain (k8s)
  jobs: 
  - deployment: Deploy_Spain
    displayName: Deploy Spain app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-spain.yaml"
      K8S.Namespace: 'ite-ms'
    environment: dev-ite-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Portugal
  dependsOn: Build_Stage
  displayName: Deploy IMAGE_CONTAINER_NAME Portugal (k8s)
  jobs: 
  - deployment: Deploy_Portugal
    displayName: Deploy Portugal app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-portugal.yaml"
      K8S.Namespace: 'itp-ms'
    environment: dev-itp-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Sweden
  dependsOn: Build_Stage
  displayName: Deploy IMAGE_CONTAINER_NAME Sweden (k8s)
  jobs: 
  - deployment: Deploy_Sweden
    displayName: Deploy Sweden app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-sweden.yaml"
      K8S.Namespace: 'its-ms'
    environment: dev-its-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Denmark
  dependsOn: Build_Stage
  displayName: Deploy IMAGE_CONTAINER_NAME Denmark (k8s)
  jobs: 
  - deployment: Deploy_Denmark
    displayName: Deploy Denmark app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-denmark.yaml"
      K8S.Namespace: 'itd-ms'
    environment: dev-itd-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml