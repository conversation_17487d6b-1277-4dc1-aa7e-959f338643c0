{"author": "Valentin CORGIER", "classifications": [".NET core", "Microservices"], "name": "Interflora .Net Core Microservice project", "identity": "ITF.MS", "shortName": "itfms", "tags": {"language": "C#"}, "sourceName": "ITF.Microservices.Template.Api", "symbols": {"sharedlib": {"type": "parameter", "datatype": "string", "replaces": "SHAREDLIB_FRAMEWORK_VERSION", "defaultValue": "8.9.2", "description": "The shared library version to use."}, "imageContainerName": {"type": "derived", "valueSource": "name", "valueTransform": "imageContainerNameForm", "replaces": "IMAGE_CONTAINER_NAME", "description": "The name of the docker container image. Should not contains any special characters and in lowercase"}, "unleashKey": {"type": "parameter", "datatype": "string", "replaces": "UNLEASH_KEY", "defaultValue": "*:default.0f1cc0c83ac07695d565dbc4317af531d1d9dd3e864f52137da6112a", "description": "The Unleash key."}, "replicaCount": {"type": "parameter", "datatype": "integer", "replaces": "REPLICA_COUNT", "defaultValue": "2", "description": "The instance exact number of replica in K8S."}, "minReplicas": {"type": "parameter", "datatype": "integer", "replaces": "MIN_REPLICA", "defaultValue": "1", "description": "The instance minimum number of replica in K8S."}, "maxReplicas": {"type": "parameter", "datatype": "integer", "replaces": "MAX_REPLICA", "defaultValue": "5", "description": "The instance maximum number of replica in K8S."}, "minReplicasProd": {"type": "parameter", "datatype": "integer", "replaces": "MIN_REPLICA_PROD", "defaultValue": "2", "description": "The instance minimum number of replica in K8S in production environment."}, "maxReplicasProd": {"type": "parameter", "datatype": "integer", "replaces": "MAX_REPLICA_PROD", "defaultValue": "10", "description": "The instance maximum number of replica in K8S in production environment."}, "autoscalingEnabled": {"type": "parameter", "datatype": "choice", "choices": [{"choice": "true"}, {"choice": "false"}], "replaces": "AUTO_SCALING_ENABLED", "defaultValue": "true", "description": "Define wether the autoscaling is enabled or not in K8S."}, "helmVersion": {"type": "parameter", "datatype": "string", "replaces": "HELM_VERSION", "defaultValue": "3.7.1", "description": "Define helm version."}, "helmChartVersion": {"type": "parameter", "datatype": "string", "replaces": "HELM_CHART_VERSION", "defaultValue": "1.4.6", "description": "Define helm chart version."}}, "forms": {"imageContainerNameForm": {"identifier": "chain", "steps": ["removeNameStart", "removeDots", "lowerCase"]}, "removeNameStart": {"identifier": "replace", "pattern": "\\.Microservices\\.", "replacement": ""}, "removeDots": {"identifier": "replace", "pattern": "\\.", "replacement": ""}}}