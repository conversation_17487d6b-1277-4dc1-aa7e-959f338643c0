// <auto-generated/>
global using global::CSharpFunctionalExtensions;
global using global::ITF.Lib.Common.Error;
global using global::ITF.Microservices.Template.Api.Reactor.Common;
global using global::ITF.SharedLibraries.ApplicationMetrics.Extensions;
global using global::ITF.SharedLibraries.EnvironmentVariable;
global using global::ITF.SharedLibraries.ExtensionMethods;
global using global::ITF.SharedLibraries.FeatureFlags;
global using global::ITF.SharedLibraries.Framework;
global using global::ITF.SharedLibraries.HealthCheck.Extensions;
global using global::ITF.SharedLibraries.HostBuilder.Extensions;
global using global::ITF.SharedLibraries.Json;
global using global::ITF.SharedLibraries.Readyness.Extensions;
global using global::ITF.SharedLibraries.Swagger;
global using global::Microsoft.AspNetCore.Mvc;
global using global::Microsoft.AspNetCore.Mvc.ApiExplorer;
global using global::Newtonsoft.Json;
global using global::Newtonsoft.Json.Linq;
global using global::Prometheus;
global using global::Swashbuckle.AspNetCore.Annotations;
global using global::System.Reflection;
