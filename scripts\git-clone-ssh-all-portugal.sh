#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
ITE.AvailabilityRules
ITF.InfrastructureAndSettings
ITF.Lib.Common
ITF.SharedLibraries
ITF.SharedModels
ITP.AvailabilityRules
ITF.AvailabilityRules
ITI2.AvailabilityRules
ITS.AvailabilityRules
IT.SharedLibraries.CT
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  <NAME_EMAIL>:v3/interflorad365fo/$REPOSITORY/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-<NAME_EMAIL>:v3/interflorad365fo/$REPOSITORY/$REPOSITORY
  git -C $REPOSITORY pull
done
  
REPOSITORIESOCTO=(
IT.Microservices.Availability
IT.Microservices.CT.Order.Synchronizer
IT.Microservices.CT.Product.Synchronizer
ITE.Microservices.Availability.CatalogSubset.Synchronizer
ITE.Microservices.OrderSubset.Synchronizer
ITE.Microservices.ProductSubset.Synchronizer
ITP.Microservices.Availability.CatalogSubset.Synchronizer
ITP.Microservices.OrderSubset.Synchronizer
ITP.Microservices.ProductSubset.Synchronizer
ITI2.Microservices.Availability.CatalogSubset.Synchronizer
ITI2.Microservices.OrderSubset.Synchronizer
ITI2.Microservices.ProductSubset.Synchronizer
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  <NAME_EMAIL>:v3/OCTO-MS/$REPOSITORY/$REPOSITORY ../src/$REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-<NAME_EMAIL>:v3/OCTO-MS/$REPOSITORY/$REPOSITORY 
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done
