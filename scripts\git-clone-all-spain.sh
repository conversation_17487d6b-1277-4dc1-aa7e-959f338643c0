#!/bin/bash

# Create the src dir if not exist
mkdir -p ../src
cd ../src

REPOSITORIES=(
ITE.AvailabilityRules
ITF.InfrastructureAndSettings
ITF.Lib.Common
ITF.SharedLibraries
ITF.SharedModels
ITP.AvailabilityRules
ITF.AvailabilityRules
ITI2.AvailabilityRules
ITS.AvailabilityRules
IT.SharedLibraries.CT
)

for REPOSITORY in ${REPOSITORIES[*]}
do
  echo ========================================================
  echo Cloning repository: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY $REPOSITORY
  echo ========================================================
  echo Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/interflorad365fo/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY pull
done
  
REPOSITORIESOCTO=(
IT.Microservices.Availability
IT.Microservices.CT.Order.Synchronizer
IT.Microservices.CT.Product.Synchronizer
ITE.Microservices.Availability.CatalogSubset.Synchronizer
ITE.Microservices.OrderSubset.Synchronizer
ITE.Microservices.ProductSubset.Synchronizer
ITP.Microservices.Availability.CatalogSubset.Synchronizer
ITP.Microservices.OrderSubset.Synchronizer
ITP.Microservices.ProductSubset.Synchronizer
ITI2.Microservices.Availability.CatalogSubset.Synchronizer
ITI2.Microservices.OrderSubset.Synchronizer
ITI2.Microservices.ProductSubset.Synchronizer
)

for REPOSITORY in ${REPOSITORIESOCTO[*]}
do
  echo ========================================================
  echo Cloning repository from OCTOMS: $REPOSITORY
  echo ========================================================
  git clone https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY $REPOSITORY
  echo ========================================================
  echo Checkout master + Pulling repository: $REPOSITORY
  echo ========================================================
  git -C $REPOSITORY remote set-url origin https://<EMAIL>/OCTO-MS/$REPOSITORY/_git/$REPOSITORY
  git -C $REPOSITORY checkout master
  git -C $REPOSITORY pull
  git checkout master
done
